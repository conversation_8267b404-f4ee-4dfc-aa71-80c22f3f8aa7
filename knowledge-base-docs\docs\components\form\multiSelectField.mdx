---
title: MultiSelectField 下拉多选
order: 5
---

# MultiSelectField 下拉多选

## 何时使用

- 用于替代原生 select，在限定的可选性内进行选择，核心能力是 选择。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/multi-select-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string[]',
      default: '-',
      desc: '当前组件默认值',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请选择'`,
      desc: '表单组件占位提示信息',
    },
    {
      code: 'hasArrow',
      type: 'boolean',
      default: 'true',
      desc: '设置是否有下拉箭头',
    },
    {
      code: 'showSearch',
      type: 'boolean',
      default: 'true',
      desc: '展开后是否能搜索，tag 模式下固定为 true',
    },
    {
      code: 'onVisibleChange',
      type: '(visible: boolean) => void',
      default: '-',
      desc: '弹层显示或隐藏时触发事件',
    },
    {
      code: 'dataSource',
      type: '[DataSource[]](/docs/components/interface#datasource)',
      default: `~~~json
[
  {
    "text": "选项一",
    "value": "1"
  },
  {
    "text": "选项二",
    "value": "2"
  },
  {
    "text": "选项三",
    "value": "3"
  }
]
      `,
      desc: '设置选项',
    },
    {
      code: 'hasSelectAll',
      type: 'boolean',
      default: 'false',
      desc: '是否有全选功能',
    },
    {
      code: 'autoWidth',
      type: 'boolean',
      default: 'true',
      desc: '下拉菜单是否与选择器对齐',
    },
    {
      code: 'showSearch',
      type: 'boolean',
      default: 'true',
      desc: '展开后是否能搜索，tag 模式下固定为 true',
    },
    {
      code: 'filter',
      type: '(value: string[], data: DataSource) => boolean',
      default: '-',
      desc: '本地过滤方法，开启本地过滤时才有',
    },
    {
      code: 'filterLocal',
      type: 'boolean',
      default: 'true',
      desc: '在数据源为远程的时候需要关闭此项，返回一个 Boolean 值确定是否保留',
    },
    {
      code: 'useDetailValue',
      type: 'boolean',
      default: 'false',
      desc: '设置value 使用对象类型{label, value}',
    },
    {
      code: 'notFoundContent',
      type: 'string',
      default: '-',
      desc: '为空文案',
    },
    {
      code: 'searchDelay',
      type: 'number',
      default: '300',
      desc: '搜索延时时间，单位ms',
    },
    {
      code: 'hasBorder',
      type: 'boolean',
      default: 'true',
      desc: '是否有边框',
    },
    {
      code: 'onChange',
      type: '({value: string, actionType:string, item:Data}) => void',
      default: '-',
      desc: `组件值发生改变事件，actionType为触发方式，可用值有： 'itemClick', 'enter', 'change'`,
    },
    {
      code: 'onVisibleChange',
      type: '(visible: boolean) => void',
      default: '-',
      desc: '弹层显示或隐藏时触发事件',
    },
    {
      code: 'onRemove',
      type: '(item: DataSource) => void',
      default: '-',
      desc: '值删除时事件',
    },
    {
      code: 'onSearch',
      type: '(keyword: string) => void',
      default: '-',
      desc: '搜索框值变化时事件',
    },
  ]}
/>
