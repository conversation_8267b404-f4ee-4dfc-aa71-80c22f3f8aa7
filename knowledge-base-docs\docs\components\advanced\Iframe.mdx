---
title: Iframe
order: 3
---

# Iframe

Iframe 组件允许你将其他网页的内容嵌入到你当前的设计器页面中。

## 何时使用

- 用于在当前页面嵌入并展示其他外部页面的内容。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/iframe-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'title',
      type: 'string',
      default: `'iFrame 组件'`,
      desc: 'Iframe 组件的标题',
    },
    {
      code: 'src',
      type: 'string',
      default: `'https://www.aliwork.com/'`,
      desc: 'Iframe组件所展示的网页地址链接',
    },
    {
      code: 'autoFit',
      type: 'boolean',
      default: 'false',
      desc: '自动调整高度，高度值=窗口高度-顶部留空高度-额外内容区高度',
    },
    {
      code: 'extraHeight',
      type: 'number',
      default: '0',
      desc: '额外内容区高度',
    },
    {
      code: 'offsetTop',
      type: 'number',
      default: '0',
      desc: '顶部空白高度，启动了自适应高度后才生效，确定顶部留下的空白区域的高度',
    },
  ]}
/>