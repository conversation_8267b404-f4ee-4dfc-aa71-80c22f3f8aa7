# 宜搭开发者中心 - 知识库文档汇总

## 文档统计

本知识库共包含 **113** 个 Markdown 文档文件，涵盖了宜搭平台的完整开发文档。

## 详细文档结构

### 1. 根目录文档
- `README.md` - 项目主要说明文档

### 2. API 文档 (docs/api/)
- `about.md` - API 介绍
- `dingAPI.md` - 钉钉 API 接口文档
- `openAPI.md` - 开放 API 文档
- `serverAPI.md` - 服务端 API 文档
- `yidaAPI.md` - 宜搭 API 文档

### 3. 组件文档 (docs/components/)

#### 3.1 高级组件 (advanced/)
- `HTML.mdx` - HTML 组件
- `Iframe.mdx` - Iframe 组件
- `JSX.mdx` - JSX 组件
- `balloon.mdx` - 气泡组件
- `bannerContainer.mdx` - 横幅容器
- `filter.mdx` - 过滤器组件
- `menu.mdx` - 菜单组件
- `pagination.mdx` - 分页组件
- `progress.mdx` - 进度条组件
- `search.mdx` - 搜索组件
- `slider.mdx` - 滑块组件
- `steps.mdx` - 步骤条组件
- `table.mdx` - 表格组件
- `timeLine.mdx` - 时间轴组件
- `tree.mdx` - 树形组件

#### 3.2 基础组件 (basic/)
- `button.mdx` - 按钮组件
- `dialog.mdx` - 对话框组件
- `drawer.mdx` - 抽屉组件
- `icon.mdx` - 图标组件
- `image.mdx` - 图片组件
- `link.mdx` - 链接组件
- `linkBlock.mdx` - 链接块组件
- `text.mdx` - 文本组件
- `video.mdx` - 视频组件

#### 3.3 表单组件 (form/)
- `attachmentField.mdx` - 附件字段
- `cascadeDateField.mdx` - 级联日期字段
- `cascadeSelectField.mdx` - 级联选择字段
- `checkboxField.mdx` - 复选框字段
- `dateField.mdx` - 日期字段
- `editorField.mdx` - 编辑器字段
- `employeeField.mdx` - 员工字段
- `imageField.mdx` - 图片字段
- `multiSelectField.mdx` - 多选字段
- `numberField.mdx` - 数字字段
- `radioField.mdx` - 单选字段
- `rateField.mdx` - 评分字段
- `selectField.mdx` - 选择字段
- `tableField.mdx` - 表格字段
- `textField.mdx` - 文本字段
- `textareaField.mdx` - 文本域字段

#### 3.4 布局组件 (layout/)
- `container.mdx` - 容器组件
- `regionalContainer.mdx` - 区域容器
- `tabsLayout.mdx` - 标签页布局

#### 3.5 组件接口
- `interface.md` - 组件接口说明

### 4. 使用指南 (docs/guide/)

#### 4.1 核心概念 (concept/)
- `condition.md` - 条件渲染
- `connector.md` - 连接器
- `customComponent.md` - 自定义组件
- `datasource.md` - 数据源
- `debug.md` - 调试
- `event.md` - 事件处理
- `lifecycle.md` - 生命周期
- `loop.md` - 循环渲染
- `state.md` - 状态管理
- `style.md` - 样式
- `validation.md` - 数据校验

#### 4.2 自定义组件 (customComponent/)
- `attributes.md` - 属性配置
- `debug.md` - 调试指南
- `FAQ.md` - 常见问题
- `manage.md` - 管理
- `start.md` - 快速开始
- `typeAndSetter.md` - 类型和设置器

#### 4.3 常见问题 (FAQ/)
- `q1.md` - 问题1
- `q2.md` - 问题2
- `q3.md` - 问题3
- `q4.md` - 问题4

#### 4.4 主要指南
- `about.md` - 平台介绍
- `contributing.md` - 贡献指南
- `designer.md` - 设计器使用
- `keywords.md` - 关键词说明
- `start.md` - 快速开始

### 5. 教程 (docs/tutorial/)
- `todoMVC.md` - TodoMVC 完整教程

### 6. 使用说明 (docs/usage/)

#### 6.1 创建应用指南 (guide/createApp/)
- `createFromBlank.md` - 从空白创建
- `createFromExcel.md` - 从Excel创建
- `createFromTemplate.md` - 从模板创建
- `index.md` - 创建应用概述

#### 6.2 平台功能 (guide/platform/)
- `appCenter.md` - 应用中心
- `authorization.md` - 授权管理
- `basicInfo.md` - 基本信息
- `connectorFactory.md` - 连接器工厂
- `corpEfficiency.md` - 企业效率
- `customDevelopment.md` - 自定义开发
- `distribute.md` - 分发管理
- `isvSolution.md` - ISV解决方案
- `myApp.md` - 我的应用
- `newApp.md` - 新建应用
- `orderCenter.md` - 订单中心
- `permission.md` - 权限管理
- `role.md` - 角色管理
- `service.md` - 服务管理
- `start.md` - 开始使用
- `taskCenter.md` - 任务中心

#### 6.3 平台介绍 (intro/)
- `about.md` - 关于宜搭
- `advantage.md` - 平台优势
- `concept.md` - 核心概念
- `designer.md` - 设计器介绍
- `scene.md` - 应用场景
- `workbench.md` - 工作台

#### 6.4 其他使用文档
- `about.md` - 使用说明
- `changeLog.md` - 更新日志
- `contactUs.md` - 联系我们
- `dataSecurity.md` - 数据安全
- `keyWords.md` - 关键词
- `newApp.md` - 新建应用
- `price.md` - 价格说明
- `quickStart.md` - 快速开始
- `platformManage.md` - 平台管理

### 7. 页面文档 (src/pages/)
- `markdown-page.md` - Markdown 页面示例

## 知识库特点

1. **完整性**: 涵盖了宜搭平台的所有核心功能和组件
2. **结构化**: 按功能模块清晰分类，便于查找和理解
3. **实用性**: 包含大量实际开发中的使用示例和最佳实践
4. **可扩展**: 支持 Markdown 和 MDX 格式，便于后续维护和扩展

## 使用建议

- 新手用户建议从 `docs/guide/start.md` 开始
- 开发者可重点关注 `docs/api/` 和 `docs/components/` 目录
- 遇到问题时可查看 `docs/guide/FAQ/` 目录
- 需要完整示例时可参考 `docs/tutorial/todoMVC.md`
