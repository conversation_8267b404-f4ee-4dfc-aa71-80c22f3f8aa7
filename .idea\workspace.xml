<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c0a10792-d349-4715-a442-21ebdb1e4743" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "hhnewbee"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/dingtalk-yida/developer-site.git",
    "accountId": "91ca6816-7507-46d2-a9da-55ccd8fed8cf"
  }
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 4
}]]></component>
  <component name="ProjectId" id="31LvKEXbABvZ72ww5uotYn6AVPU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "com.codeium.enabled": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/code3/yidaEidt/developer-site",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.stylelint": "D:\\code3\\yidaEidt\\developer-site\\node_modules\\stylelint",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-410509235cf1-JavaScript-WS-242.20224.426" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c0a10792-d349-4715-a442-21ebdb1e4743" name="Changes" comment="" />
      <created>1755316103010</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755316103010</updated>
      <workItem from="1755316103707" duration="5013000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>