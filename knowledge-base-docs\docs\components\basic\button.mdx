---
title: Button 按钮
order: 2
---

# Button 按钮

按钮用于开始一个即时操作。

## 何时使用

- 标记一个（或封装一组）操作命令，响应用户点击行为，触发相应的业务逻辑。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/button-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'content',
      type: 'string',
      default: `'按钮'`,
      desc: '按钮文案内容',
    },
    {
      code: 'type',
      type: `'primary' | 'normal' | 'secondary' | 'ghostLight' | 'ghostDark' | 'warningNormal' |'warningPrimary' | 'textNormal' | 'textPrimary' | 'textSecondary'`,
      default: ` 'primary' `,
      desc: '按钮的类型',
    },
    {
      code: 'size',
      type: `'small' | 'medium' | 'large'`,
      default: `'medium'`,
      desc: '按钮尺寸大小',
    },
    {
      code: 'behavior',
      type: `'NORMAL' | 'DISABLED' | 'HIDDEN'`,
      default: `'NORMAL'`,
      desc: '表单展示状态',
    },
    {
      code: 'baseIcon',
      type: 'string',
      default: '-',
      desc: '按钮基础图标，参考Icon的 **baseIcon** [属性设置](/docs/components/basic/icon#组件属性)',
    },
    {
      code: 'otherIcon',
      type: 'string',
      default: '-',
      desc: '其他图标，参考Icon的 **otherIcon** [属性设置](/docs/components/basic/icon#组件属性)',
    },
    {
      code: 'loading',
      type: 'boolean',
      default: 'false',
      desc: '是否处于加载状态',
    },
    {
      code: 'triggerEventsWhenLoading',
      type: 'boolean',
      default: 'false',
      desc: '当按钮为加载状态时，是否响应动作',
    },
    {
      code: 'contentMarginMobile',
      type: `'0' | '16'`,
      default: ` '0' `,
      desc: '手机端按钮的左右间距',
    },
  ]}
/>
