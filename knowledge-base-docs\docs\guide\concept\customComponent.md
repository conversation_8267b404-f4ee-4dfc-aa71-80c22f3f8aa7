---
title: 自定义组件
order: 11
---


| 能力 | 免费版 | 轻享版 | 专业版 | 专属版 |
| :------- | :------- | :------- | :------- | :------- |
| 自定义组件 | 不支持| 不支持 | 支持 | 支持 |

:::info
自定义组件开发需要具备一定的开发基础，面向开发者、ISV服务商、高级业务专家等。
:::

## 功能简介

### 自定义组件简介

自定义组件功能为客户提供创建组件的能力，提升组件与业务需求契合度的同时，减少组件开发及运维的成本并提高了组件的可复用性。

### 使用场景

当宜搭普通组件无法满足您应用开发的业务需求时，您可以根据不同行业的业务习惯或需求来自行开发一款贴合业务场景的组件。且同一个组织中的自定义组件是可以进行共享的，这也就为后续您开发同类型应用复用相关组件提供了便利。

### 组件的构成

组件由视图和属性构成，在组件的消费侧可以通过改变组件的属性让组件展示出不同的视图。
视图再细分可以分为设计视图和运行时视图，一般情况下设计视图和运行时视图可以复用。
![](https://img.alicdn.com/imgextra/i3/O1CN01yshAOJ1V3TK9C6tr3_!!6000000002597-2-tps-959-532.png_.webp)

在组件的生产侧，我们要分别构建组件的视图和属性。

![](https://img.alicdn.com/imgextra/i2/O1CN012OMxBD1aO9HkXB1TI_!!6000000003319-2-tps-959-530.png_.webp)

## 导入自定义组件 Schema

在示例部分，我们提供了多个[自定义组件示例](https://docs.aliwork.com/docs/yida_support/wtwabe/oupunp/gdi5p8/sg47d6/zas20t)，开发者可以通过以下步骤将示例中的 Schema 导入到自己创建的自定义组件中并在业务中使用：

### 步骤 1：创建自定组件

开发者可以按照[这个文档](/docs/guide/customComponent/start#步骤-1创建自定义组件)创建一个自定义组件用于导入示例 Schema。

### 步骤 2：打开宜搭 Schema 工作台并导入 Schema

进入自定义组件设计器，通过[这个文档](/docs/guide/concept/debug#开启-schema-工作台)的方法打开 Schema 工作台，并将自定义组件的 Schema 复制到 Schema 工作台点击导入 Schema；

### 步骤 3：安装并使用

到此自定义组件已经创建完成，接下来开发者便可以通过[这个文档](/docs/guide/customComponent/start#步骤-3安装自定义组件)来安装并使用自定义组件。

## 附录

### 组件类型

宜搭自定义组件类型分 **「普通组件」**和**「表单组件」**两种，核心区别是：

1. 普通组件一般用来做展示型的功能，没有数据存储的能力。类似现有的「图文展示」「分组」组件。
1. 表单组件可以用来做数据提交，表单组件在开发上相对普通组件会更复杂些，需要额外定义元数据的配置。
1. 表单组件暂未开放，敬请期待。

### 组件安装类型

宜搭的[页面类型](https://docs.aliwork.com/docs/yida_support/li89l7/poq66i)有 普通表单页面、流程表单页面、报表页面、DataV 大屏、自定义页面、外部链接。
宜搭的自定义组件目前支持其中的普通表单页面、流程表单页面、自定义页面这三种。
安装时选择了对应的页面类型后，在对应的页面设计器中则会看到对应安装的组件。

### 组件版本说明

宜搭的自定义组件版本分 **开发版**和**正式版**。
我们在[语义化版本](https://semver.org/lang/zh-CN/)的基础上做了额外的约定。

1. 0.1.0 默认为开发版本，开发版可和组件设计器保持实时同步，用来调试组件。
1. 1.x.x 为发布后的正式版本，会固定当前版本的功能，保证线上使用该组件的稳定性。**所以线上的正式应用请务必安装 1.x.x 发布后的正式版本。**
