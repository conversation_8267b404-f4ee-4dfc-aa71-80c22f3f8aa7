---
title: TablePc 表格
order: 5
---

# TablePc 表格

展示行列数据。

## 何时使用

- Table 负责将数据呈现为高度可定制和具备可访问性的 HTML 表格，其核心功能为将结构化的数据使用表格的方式展现;
- 可以使用各种参数来向表格中加入一些特性，比如排序，过滤，滚动，锁列等。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/table-pc-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'columns',
      type: '[TableColumn[]](/docs/components/interface#tablecolumn)',
      default: '-',
      desc: '可通过绑定数据源设置数据列',
    },
    {
      code: 'dataSourceType',
      type: `'data' | 'url'`,
      default: `'data'`,
      desc: '数据源类型',
    },
    {
      code: 'data',
      type: 'any[]',
      default: '-',
      desc: '数据源',
    },
    {
      code: 'primaryKey',
      type: 'string',
      default: `'id'`,
      desc: '设置数据主键，数据主键用于区分数据中不同的行，对行选择和行编辑功能非常重要，不同的行主键值不可重复，一般采用数据库中自增 ID 字段',
    },
    {
      code: 'loading',
      type: 'boolean',
      default: 'false',
      desc: '是否处于加载状态',
    },
    {
      code: 'actionTitle',
      type: 'string',
      default: `'操作'`,
      desc: '设置操作列标题',
    },
    {
      code: 'actionWidth',
      type: 'number',
      default: '0',
      desc: '设置操作列宽度',
    },
    {
      code: 'actionType',
      type: 'string',
      default: `'link'`,
      desc: '设置操作列按钮类型',
    },
    {
      code: 'actionFixed',
      type: 'string',
      default: `'none'`,
      desc: '设置操作列列固定',
    },
    {
      code: 'actionHidden',
      type: 'boolean',
      default: 'false',
      desc: '设置操作列是否隐藏',
    },
    {
      code: 'maxWebShownActionCount',
      type: 'number',
      default: '3',
      desc: '设置最大展示数量',
    },
    {
      code: 'title',
      type: 'string',
      default: `'详情'`,
      desc: '设置操作项-标题',
    },
    {
      code: 'mode',
      type: 'string',
      default: `'VIEW'`,
      desc: '设置操作项-在编辑状态显示',
    },
    {
      code: 'device',
      type: 'string',
      default: '-',
      desc: '设置操作项-在特定端显示',
    },
    {
      code: 'render',
      type: '(title: object,rowData: object)=>{title}',
      default: '-',
      desc: '设置操作项-定制渲染',
    },
    {
      code: 'showActionBar',
      type: 'boolean',
      default: 'true',
      desc: '设置顶部操作-显示操作条',
    },
    {
      code: 'actionBar',
      type: 'array',
      default: "[{title: {zh_CN: '操作1',en_US: 'Action 1',type: 'i18n'},option: 'callback'}]",
      desc: '设置顶部操作-操作条',
    },
    {
      code: 'title',
      type: 'string',
      default: `'操作'`,
      desc: '设置顶部操作-操作条标题',
    },
    {
      code: 'pageMode',
      type: 'string',
      default: '-',
      desc: '设置顶部操作-操作条页面跳转,关联页面状态',
    },
    {
      code: 'isDisabled',
      type: 'boolean',
      default: '-',
      desc: '设置顶部操作-操作条页面跳转,是否禁用',
    },
    {
      code: 'showLinkBar',
      type: 'boolean',
      default: 'true',
      desc: '设置顶部操作-是否显示外链条',
    },
    {
      code: 'title',
      type: 'string',
      default: `'外链操作'`,
      desc: '设置顶部操作-外链操作条,外链操作标题',
    },
    {
      code: 'showSearch',
      type: 'boolean',
      default: 'true',
      desc: '设置顶部操作-显示搜索条',
    },
    {
      code: 'searchBarPlaceholder',
      type: 'string',
      default: `'请搜索'`,
      desc: '设置顶部操作-显示搜索条占位符',
    },
    {
      code: 'showCustomCoulmn',
      type: 'boolean',
      default: 'false',
      desc: '设置顶部操作-显示列筛选器',
    },
    {
      code: 'onColumnsChange',
      type: '(columns: object)=>void',
      default: '-',
      desc: '设置顶部操作-列筛选器回调',
    },
    {
      code: 'showCustomBarItem',
      type: 'boolean',
      default: 'false',
      desc: '设置顶部操作-显示自定义区域',
    },
    {
      code: 'showCustomCoulmn',
      type: '()=>void',
      default: '-',
      desc: '设置顶部操作-自定义区域渲染',
    },
    {
      code: 'theme',
      type: 'string',
      default: `'split'`,
      desc: '设置风格和样式-主题',
    },
    {
      code: 'hasHeader',
      type: 'boolean',
      default: 'true',
      desc: '设置风格和样式-显示表头',
    },
    {
      code: 'fixedHeader',
      type: 'boolean',
      default: 'true',
      desc: '设置风格和样式-表头是否固定',
    },
    {
      code: 'maxBodyHeight',
      type: 'number',
      default: '0',
      desc: '设置风格和样式-表头是否固定,最大内容区域的高度',
    },
    {
      code: 'stickyHeader',
      type: 'boolean',
      default: 'false',
      desc: '设置风格和样式-表头是否是sticky',
    },
    {
      code: 'useVirtual',
      type: 'boolean',
      default: 'false',
      desc: '设置风格和样式-虚拟滚动',
    },
    {
      code: 'setLoadingComponent',
      type: 'boolean',
      default: 'false',
      desc: '设置风格和样式-自定义loading组件',
    },
    {
      code: 'loadingComponent',
      type: '()=>ReactNode',
      default: '-',
      desc: '设置风格和样式-设置loading组件',
    },
    {
      code: 'cellProps',
      type: '(rowIndex: number,colIndex: number,dataIndex: number,record: object)=>void',
      default: '-',
      desc: '设置风格和样式-单元格分割合并',
    },
    {
      code: 'rowProps',
      type: '(record: object,index: number)=>void',
      default: '-',
      desc: '设置风格和样式-单元格行属性配置',
    },
    {
      code: 'setEmptyContent',
      type: 'boolean',
      default: 'false',
      desc: '设置风格和样式-自定义空数据渲染',
    },
    {
      code: 'emptyContent',
      type: '() => ReactNode',
      default: '-',
      desc: '设置风格和样式-空数据渲染',
    },
    {
      code: 'showRowSelector',
      type: 'boolean',
      default: 'false',
      desc: '设置行选择器-是否显示',
    },
    {
      code: 'mode',
      type: `'single' | 'multiple'`,
      default: `'multiple'`,
      desc: '设置行选择器-类型',
    },
    {
      code: 'selectedRowKeys',
      type: 'string[]',
      default: '-',
      desc: '设置行选择器-已选中的行',
    },
    {
      code: 'onChange',
      type: '(selectedRowKeys: array,records: array)=>void',
      default: '-',
      desc: '设置行选择器-选择变动回调',
    },
    {
      code: 'onSelect',
      type: '({selected: boolean,rowData: object,selectedRows: array})=>void',
      default: '-',
      desc: '设置行选择器-单行选择回调',
    },
    {
      code: 'onSelectAll',
      type: '(selected: boolean,selectedRows: array)=>void',
      default: '-',
      desc: '设置行选择器-全部选择回调',
    },
    {
      code: 'columnProps',
      type: '() => void',
      default: '-',
      desc: '设置行选择器-选择列属性',
    },
    {
      code: 'getProps',
      type: '(rowData: object, index: number) => void',
      default: '-',
      desc: '设置行选择器-选择器属性',
    },
    {
      code: 'titleProps',
      type: '(rowData: object, index: number) => void',
      default: '',
      desc: '设置行选择器-选择列列标题属性',
    },
    {
      code: 'titleAddons',
      type: '(rowData: object,index: number) => ReactNode',
      default: '',
      desc: '设置行选择器-选择列列标题元素',
    },
    {
      code: 'isPagination',
      type: 'boolean',
      default: 'true',
      desc: '分页设置-使用分页',
    },
    {
      code: 'pagination',
      type: 'string',
      default: `'right'`,
      desc: '分页设置-分页位置',
    },
    {
      code: 'size',
      type: 'string',
      default: `'medium'`,
      desc: '分页设置-分页尺寸',
    },
    {
      code: 'type',
      type: 'string',
      default: `'normal'`,
      desc: '分页设置-分页类型',
    },
    {
      code: 'shape',
      type: 'string',
      default: 'arrow-only',
      desc: '分页设置-前进后退按钮样式',
    },
    {
      code: 'pageSizeSelector',
      type: 'boolean',
      default: 'false',
      desc: '分页设置-每页显示选择器类型',
    },
    {
      code: 'pageSizeList',
      type: 'array',
      default: '[5,10,20]',
      desc: '分页设置-每页显示选择器可选值',
    },
    {
      code: 'pageSize',
      type: 'number',
      default: '10',
      desc: '分页设置-pageSize',
    },
    {
      code: 'pageSizePosition',
      type: 'string',
      default: `'end'`,
      desc: '分页设置-每页显示选择器在组件中的位置',
    },
    {
      code: 'pageShowCount',
      type: 'number',
      default: '5',
      desc: '分页设置-页码显示数量',
    },
    {
      code: 'isPagination',
      type: 'boolean',
      default: 'false',
      desc: '分页设置-当分页数为1时，是否隐藏分页器',
    },
    {
      code: 'showMiniPager',
      type: 'boolean',
      default: 'false',
      desc: '分页设置-是否显示顶部的迷你分页(仅对 PC 端渲染有效)',
    },
    {
      code: 'isExpand',
      type: 'boolean',
      default: 'false',
      desc: '设置可折叠/树形表格-启用折叠',
    },
    {
      code: 'hasExpandedRowCtrl',
      type: 'boolean',
      default: 'true',
      desc: '设置可折叠/树形表格-启用折叠,折叠按钮',
    },
    {
      code: 'expandedRowIndent',
      type: 'array',
      default: '[]',
      desc: '设置可折叠/树形表格-启用折叠,额外渲染行的缩进',
    },
    {
      code: 'expandedRowRender',
      type: '(record: object, index: number) => ReactNode',
      default: '-',
      desc: '设置可折叠/树形表格-启用折叠,额外渲染行的渲染函数',
    },
    {
      code: 'getExpandedColProps',
      type: '(record: object, index: number) => ReactNode',
      default: '-',
      desc: '设置可折叠/树形表格-启用折叠,设置额外渲染行的属性',
    },
    {
      code: 'openRowKeys',
      type: 'string[]',
      default: '-',
      desc: '设置可折叠/树形表格-启用折叠,默认展开渲染的行',
    },
    {
      code: 'onRowOpen',
      type: '(openRowKeys: array, currentRowKey: string, expanded: boolean, currentRecord: object) => void',
      default: '-',
      desc: '设置可折叠/树形表格-启用折叠,展开收起时触发的事件',
    },
    {
      code: 'isTree',
      type: 'boolean',
      default: 'false',
      desc: '设置可折叠/树形表格-启用树形',
    },
    {
      code: 'mobileMode',
      type: 'string',
      default: 'normal',
      desc: '手机端特有配置-展示风格',
    },
    {
      code: 'mobileExpanViewMode',
      type: 'string',
      default: 'normal',
      desc: '手机端特有配置-展开方式',
    },
    {
      code: 'mobileDefaultCardColumns',
      type: 'string',
      default: '4',
      desc: '手机端特有配置-展开方式,默认字段数',
    },
    {
      code: 'mobileActionsStyle',
      type: 'string',
      default: '-',
      desc: '手机端特有配置-设置展开方式,操作风格',
    },
    {
      code: 'mobileMargin',
      type: 'number',
      default: '0',
      desc: '手机端特有配置-设置默认外边距',
    },
    {
      code: 'noPadding',
      type: 'boolean',
      default: 'false',
      desc: '高级-设置隐藏边距',
    },
    {
      code: 'useStickyLock',
      type: 'boolean',
      default: 'false',
      desc: '高级-设置列固定优化',
    },
    {
      code: 'onFetchData',
      type: '({params: object}) => void',
      default: '-',
      desc: '动作设置-分页、搜索、排序时触发',
    },
    {
      code: 'onCellDataChange',
      type: '({data: object}) => void',
      default: '-',
      desc: '动作设置-编态数据发生变化时触发',
    },
    {
      code: 'onRowClick',
      type: '({record: object, index:number, evt:object}) => void',
      default: '-',
      desc: '动作设置-点击表格每一行触发的事件',
    },
    {
      code: 'onRowMouseEnter',
      type: '({record: object, index: number, evt: object}) => void',
      default: '-',
      desc: '动作设置-悬浮在表格每一行的时候触发的事件',
    },
    {
      code: 'onRowMouseLeave',
      type: '({record: object, index:number, evt:object}) => void',
      default: '-',
      desc: '动作设置-离开表格每一行的时候触发的事件',
    },
    {
      code: 'onResizeChange',
      type: '({dataIndex: string, value:number}) => void',
      default: '-',
      desc: '动作设置-重设列尺寸的时候触发的事件',
    },
  ]}
/>
