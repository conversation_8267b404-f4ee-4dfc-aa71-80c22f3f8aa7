# 宜搭开发模式与最佳实践

## 🎨 开发模式详解

### 1. 可视化开发模式

#### 1.1 拖拽式开发
```
设计器界面布局：
┌─────────────┬─────────────────────┬─────────────┐
│  组件面板   │      画布区域        │  属性面板   │
│             │                     │             │
│ - 基础组件  │   ┌─────────────┐   │ - 组件属性  │
│ - 表单组件  │   │   页面内容   │   │ - 样式设置  │
│ - 高级组件  │   │             │   │ - 事件配置  │
│ - 布局组件  │   └─────────────┘   │ - 数据绑定  │
└─────────────┴─────────────────────┴─────────────┘
```

#### 1.2 组件配置流程
1. **选择组件**: 从组件面板选择合适的组件
2. **拖拽放置**: 将组件拖拽到画布指定位置
3. **属性配置**: 在属性面板配置组件属性
4. **样式设置**: 设置组件的外观样式
5. **事件绑定**: 配置组件的交互事件
6. **数据绑定**: 绑定动态数据源

#### 1.3 布局设计原则
```css
/* 响应式布局 */
.container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
}
```

### 2. 代码开发模式

#### 2.1 JavaScript开发
```javascript
// 页面初始化
export function didMount() {
  // 初始化数据
  this.initializeData();
  
  // 设置定时器
  this.timer = setInterval(() => {
    this.refreshData();
  }, 30000);
}

// 数据初始化
export function initializeData() {
  this.setState({
    loading: true,
    dataList: [],
    currentPage: 1,
    pageSize: 10
  });
  
  this.loadData();
}

// 加载数据
export function loadData() {
  const { currentPage, pageSize } = this.state;
  
  this.dataSourceMap.getDataList.load({
    page: currentPage,
    size: pageSize
  }).then(res => {
    if (res && res.success) {
      this.setState({
        dataList: res.data.list,
        total: res.data.total,
        loading: false
      });
    }
  }).catch(err => {
    this.utils.toast({
      type: 'error',
      title: '数据加载失败',
      message: err.message
    });
    this.setState({ loading: false });
  });
}
```

#### 2.2 样式开发
```scss
// 使用SCSS变量
$primary-color: #1890ff;
$border-radius: 4px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// 组件样式
.custom-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 16px;
  margin-bottom: 16px;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .card-content {
    color: #666;
    line-height: 1.6;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-card {
    margin: 8px;
    padding: 12px;
    
    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
}
```

### 3. 混合开发模式

#### 3.1 可视化 + 代码结合
```javascript
// 在可视化组件基础上添加自定义逻辑
export function onTableRowClick(record, index) {
  // 获取可视化配置的表格组件
  const tableComponent = this.$('dataTable');
  
  // 自定义行点击逻辑
  this.setState({
    selectedRecord: record,
    selectedIndex: index
  });
  
  // 显示详情弹窗（可视化配置的弹窗组件）
  this.$('detailDialog').setVisible(true);
  
  // 加载详情数据
  this.loadRecordDetail(record.id);
}

// 表单验证结合
export function validateForm() {
  const formData = this.getFormData();
  const errors = {};
  
  // 自定义验证逻辑
  if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
    errors.email = '请输入有效的邮箱地址';
  }
  
  if (!formData.phone || !/^1[3-9]\d{9}$/.test(formData.phone)) {
    errors.phone = '请输入有效的手机号码';
  }
  
  // 设置验证错误到可视化表单组件
  Object.keys(errors).forEach(field => {
    this.$(`${field}Field`).setValidateMessage(errors[field]);
  });
  
  return Object.keys(errors).length === 0;
}
```

## 🏗️ 架构设计模式

### 1. MVC模式在宜搭中的应用

```javascript
// Model - 数据模型
const UserModel = {
  // 获取用户列表
  async getUserList(params) {
    return await this.dataSourceMap.getUserList.load(params);
  },
  
  // 创建用户
  async createUser(userData) {
    return await this.dataSourceMap.createUser.load(userData);
  },
  
  // 更新用户
  async updateUser(id, userData) {
    return await this.dataSourceMap.updateUser.load({ id, ...userData });
  }
};

// View - 视图更新
const UserView = {
  // 渲染用户列表
  renderUserList(userList) {
    this.$('userTable').setDataSource(userList);
  },
  
  // 显示加载状态
  showLoading() {
    this.$('userTable').setLoading(true);
  },
  
  // 隐藏加载状态
  hideLoading() {
    this.$('userTable').setLoading(false);
  }
};

// Controller - 控制器
const UserController = {
  // 加载用户列表
  async loadUserList() {
    try {
      UserView.showLoading();
      const result = await UserModel.getUserList({
        page: this.state.currentPage,
        size: this.state.pageSize
      });
      
      if (result.success) {
        UserView.renderUserList(result.data.list);
        this.setState({
          total: result.data.total
        });
      }
    } catch (error) {
      this.utils.toast({
        type: 'error',
        title: '加载失败',
        message: error.message
      });
    } finally {
      UserView.hideLoading();
    }
  }
};
```

### 2. 组件化设计模式

```javascript
// 可复用的业务组件
const BusinessComponents = {
  // 用户选择器组件
  UserSelector: {
    // 初始化用户选择器
    init(containerId, options = {}) {
      const container = this.$(containerId);
      
      // 设置选择器配置
      container.setProps({
        placeholder: options.placeholder || '请选择用户',
        multiple: options.multiple || false,
        searchable: true
      });
      
      // 加载用户数据
      this.loadUserOptions(containerId);
    },
    
    // 加载用户选项
    async loadUserOptions(containerId) {
      try {
        const result = await this.dataSourceMap.getUserOptions.load();
        if (result.success) {
          this.$(containerId).setOptions(result.data.map(user => ({
            label: user.name,
            value: user.id
          })));
        }
      } catch (error) {
        console.error('加载用户选项失败:', error);
      }
    }
  },
  
  // 数据表格组件
  DataTable: {
    // 初始化数据表格
    init(tableId, config) {
      const table = this.$(tableId);
      
      // 设置表格配置
      table.setProps({
        columns: config.columns,
        pagination: config.pagination !== false,
        rowSelection: config.rowSelection || null
      });
      
      // 绑定事件
      if (config.onRowClick) {
        table.on('rowClick', config.onRowClick);
      }
    },
    
    // 刷新表格数据
    refresh(tableId, params = {}) {
      const table = this.$(tableId);
      table.setLoading(true);
      
      // 重新加载数据
      this.loadTableData(tableId, params);
    }
  }
};
```

### 3. 状态管理模式

```javascript
// 全局状态管理
const StateManager = {
  // 初始化状态
  initState() {
    this.setState({
      // 用户相关状态
      user: {
        info: null,
        permissions: [],
        preferences: {}
      },
      
      // 应用相关状态
      app: {
        loading: false,
        error: null,
        theme: 'light'
      },
      
      // 业务相关状态
      business: {
        currentModule: 'dashboard',
        selectedItems: [],
        filters: {}
      }
    });
  },
  
  // 更新用户状态
  updateUserState(updates) {
    this.setState({
      user: {
        ...this.state.user,
        ...updates
      }
    });
  },
  
  // 更新应用状态
  updateAppState(updates) {
    this.setState({
      app: {
        ...this.state.app,
        ...updates
      }
    });
  },
  
  // 设置加载状态
  setLoading(loading) {
    this.updateAppState({ loading });
  },
  
  // 设置错误状态
  setError(error) {
    this.updateAppState({ error });
  }
};
```

## 🔄 数据流管理模式

### 1. 单向数据流

```javascript
// 数据流：Action -> State -> View
const DataFlow = {
  // Action - 动作定义
  actions: {
    // 加载数据动作
    async LOAD_DATA(params) {
      this.dispatch('SET_LOADING', true);
      
      try {
        const result = await this.dataSourceMap.getData.load(params);
        if (result.success) {
          this.dispatch('SET_DATA', result.data);
        } else {
          this.dispatch('SET_ERROR', result.message);
        }
      } catch (error) {
        this.dispatch('SET_ERROR', error.message);
      } finally {
        this.dispatch('SET_LOADING', false);
      }
    },
    
    // 更新数据动作
    async UPDATE_DATA(id, updates) {
      try {
        const result = await this.dataSourceMap.updateData.load({ id, ...updates });
        if (result.success) {
          this.dispatch('UPDATE_ITEM', { id, updates });
          this.utils.toast({
            type: 'success',
            title: '更新成功'
          });
        }
      } catch (error) {
        this.dispatch('SET_ERROR', error.message);
      }
    }
  },
  
  // Dispatch - 分发器
  dispatch(type, payload) {
    switch (type) {
      case 'SET_LOADING':
        this.setState({ loading: payload });
        break;
        
      case 'SET_DATA':
        this.setState({ 
          dataList: payload,
          error: null 
        });
        this.updateView();
        break;
        
      case 'SET_ERROR':
        this.setState({ 
          error: payload,
          loading: false 
        });
        this.showError(payload);
        break;
        
      case 'UPDATE_ITEM':
        const { id, updates } = payload;
        const newDataList = this.state.dataList.map(item => 
          item.id === id ? { ...item, ...updates } : item
        );
        this.setState({ dataList: newDataList });
        this.updateView();
        break;
    }
  },
  
  // View Update - 视图更新
  updateView() {
    const { dataList, loading } = this.state;
    
    // 更新表格数据
    this.$('dataTable').setDataSource(dataList);
    this.$('dataTable').setLoading(loading);
    
    // 更新统计信息
    this.$('totalCount').setValue(dataList.length);
  },
  
  // Error Handling - 错误处理
  showError(message) {
    this.utils.toast({
      type: 'error',
      title: '操作失败',
      message
    });
  }
};
```

### 2. 事件驱动模式

```javascript
// 事件驱动架构
const EventDriven = {
  // 事件监听器
  listeners: new Map(),
  
  // 注册事件监听
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  },
  
  // 触发事件
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        callback(data);
      });
    }
  },
  
  // 移除事件监听
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  },
  
  // 业务事件定义
  setupBusinessEvents() {
    // 用户登录事件
    this.on('user:login', (userInfo) => {
      this.setState({ user: userInfo });
      this.loadUserPermissions(userInfo.id);
    });
    
    // 数据更新事件
    this.on('data:updated', (data) => {
      this.refreshDataView();
      this.emit('ui:refresh');
    });
    
    // UI刷新事件
    this.on('ui:refresh', () => {
      this.updateAllComponents();
    });
  }
};
```

## 🎯 性能优化模式

### 1. 懒加载模式

```javascript
// 组件懒加载
const LazyLoading = {
  // 懒加载数据表格
  setupLazyTable(tableId) {
    const table = this.$(tableId);
    
    // 设置虚拟滚动
    table.setProps({
      virtual: true,
      pageSize: 50
    });
    
    // 监听滚动事件
    table.on('scroll', this.debounce((scrollInfo) => {
      if (scrollInfo.scrollTop + scrollInfo.clientHeight >= scrollInfo.scrollHeight - 100) {
        this.loadMoreData();
      }
    }, 300));
  },
  
  // 加载更多数据
  async loadMoreData() {
    if (this.state.loading || !this.state.hasMore) return;
    
    this.setState({ loading: true });
    
    try {
      const result = await this.dataSourceMap.getMoreData.load({
        page: this.state.currentPage + 1,
        size: this.state.pageSize
      });
      
      if (result.success) {
        this.setState({
          dataList: [...this.state.dataList, ...result.data.list],
          currentPage: this.state.currentPage + 1,
          hasMore: result.data.hasMore
        });
      }
    } catch (error) {
      console.error('加载更多数据失败:', error);
    } finally {
      this.setState({ loading: false });
    }
  },
  
  // 防抖函数
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
};
```

### 2. 缓存模式

```javascript
// 数据缓存管理
const CacheManager = {
  cache: new Map(),
  
  // 设置缓存
  set(key, data, ttl = 300000) { // 默认5分钟过期
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  },
  
  // 获取缓存
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  },
  
  // 清除缓存
  clear(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  },
  
  // 带缓存的数据加载
  async loadWithCache(cacheKey, loader, ttl) {
    // 先尝试从缓存获取
    let data = this.get(cacheKey);
    if (data) {
      return data;
    }
    
    // 缓存未命中，加载数据
    try {
      data = await loader();
      this.set(cacheKey, data, ttl);
      return data;
    } catch (error) {
      console.error('数据加载失败:', error);
      throw error;
    }
  }
};
```

这个开发模式指南涵盖了宜搭开发的核心模式和最佳实践，为AI系统提供了全面的开发指导。
