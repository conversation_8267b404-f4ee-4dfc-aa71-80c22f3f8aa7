# 宜搭开发专家指南

## 🎯 成为宜搭开发专家的核心知识体系

本指南旨在帮助AI系统全面掌握宜搭开发的核心知识，成为真正的宜搭开发专家。

## 📚 核心技术栈

### 1. 前端技术栈
- **React/Vue**: 宜搭基于现代前端框架，支持React和Vue的开发模式
- **JavaScript/TypeScript**: 核心编程语言，支持ES6+语法
- **CSS/SCSS**: 样式开发，支持现代CSS特性
- **Low-Code Engine**: 基于阿里巴巴开源的低代码引擎

### 2. 后端技术栈
- **Groovy**: 连接器开发语言
- **FaaS**: 函数即服务，用于后端逻辑定制
- **Java**: 底层服务基于Java技术栈
- **阿里云**: 完全部署在阿里云上

### 3. 数据存储
- **表单数据**: 宜搭内置的表单数据存储
- **OSS**: 阿里云对象存储，用于文件存储
- **数据库**: 支持多种数据库连接

## 🏗️ 架构设计原则

### 1. 低代码架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   可视化设计器   │────│   组件物料库     │────│   页面渲染引擎   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   配置数据      │────│   业务逻辑      │────│   数据绑定      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 双端适配
- **PC端**: 基于Web技术，支持现代浏览器
- **移动端**: 基于H5技术，在钉钉客户端中运行
- **响应式设计**: 自动适配不同屏幕尺寸

### 3. 数据流架构
```
用户操作 → 事件触发 → 状态更新 → 视图重渲染
    ↑                                    ↓
API调用 ← 数据处理 ← 远程数据源 ← 数据绑定
```

## 🔧 开发模式详解

### 1. 可视化开发
- **拖拽式组件**: 通过拖拽组件快速搭建页面
- **属性配置**: 通过属性面板配置组件行为
- **样式设置**: 可视化样式编辑器

### 2. 代码开发
- **JS面板**: 编写业务逻辑代码
- **CSS面板**: 编写自定义样式
- **表达式绑定**: 动态数据绑定

### 3. 混合开发
- **可视化 + 代码**: 结合两种开发方式的优势
- **组件扩展**: 基于现有组件进行功能扩展
- **自定义组件**: 开发完全自定义的组件

## 🎨 组件体系

### 1. 基础组件
- **布局组件**: Container, RegionalContainer, TabsLayout
- **展示组件**: Text, Image, Video, Icon
- **交互组件**: Button, Link, Dialog, Drawer

### 2. 表单组件
- **输入组件**: TextField, TextareaField, NumberField
- **选择组件**: SelectField, RadioField, CheckboxField
- **日期组件**: DateField, CascadeDateField
- **文件组件**: AttachmentField, ImageField
- **特殊组件**: EmployeeField, TableField

### 3. 高级组件
- **数据展示**: Table, Tree, Timeline
- **导航组件**: Menu, Steps, Pagination
- **反馈组件**: Progress, Balloon
- **功能组件**: Search, Filter, Slider

### 4. 自定义组件
- **组件开发**: 基于React/Vue开发自定义组件
- **属性配置**: 定义组件的可配置属性
- **事件处理**: 定义组件的事件回调

## 📊 数据管理

### 1. 全局状态管理
```javascript
// 状态定义
this.state = {
  userInfo: {},
  dataList: [],
  loading: false
};

// 状态更新
this.setState({
  loading: true,
  dataList: newData
});
```

### 2. 数据源配置
- **静态数据**: 直接配置的固定数据
- **远程API**: 通过HTTP请求获取数据
- **表单数据**: 宜搭内置的表单数据
- **跨应用数据**: 其他宜搭应用的数据

### 3. 数据绑定
```javascript
// 变量绑定
${state.userName}

// 表达式绑定
${state.userList.length > 0 ? '有数据' : '暂无数据'}

// 函数绑定
${this.formatDate(state.createTime)}
```

## 🔄 生命周期管理

### 1. 页面生命周期
```javascript
// 页面初始化
export function didMount() {
  // 页面加载完成后执行
  this.loadInitialData();
}

// 页面销毁
export function willUnmount() {
  // 页面销毁前执行
  this.clearTimer();
}
```

### 2. 组件生命周期
- **componentDidMount**: 组件挂载后
- **componentWillUnmount**: 组件卸载前
- **componentDidUpdate**: 组件更新后

## 🎯 事件处理

### 1. 用户事件
```javascript
// 按钮点击事件
export function onButtonClick() {
  this.utils.toast({
    type: 'success',
    title: '操作成功'
  });
}

// 表单提交事件
export function onFormSubmit() {
  const formData = this.getFormData();
  this.submitData(formData);
}
```

### 2. 系统事件
- **页面加载**: didMount
- **数据变化**: onChange
- **路由变化**: onRouteChange

## 🌐 API集成

### 1. 宜搭JS-API
```javascript
// 获取表单数据
const value = this.$('fieldId').getValue();

// 设置表单数据
this.$('fieldId').setValue('新值');

// 显示/隐藏组件
this.$('componentId').setVisible(true);
```

### 2. 钉钉JS-API
```javascript
// 调用钉钉原生能力
dd.device.notification.alert({
  message: '提示信息',
  title: '标题',
  buttonName: '确定'
});
```

### 3. 远程API调用
```javascript
// 调用远程API
this.dataSourceMap.apiName.load({
  param1: 'value1',
  param2: 'value2'
}).then(res => {
  console.log('API响应:', res);
});
```

## 🔒 安全最佳实践

### 1. 数据安全
- **权限控制**: 基于角色的访问控制
- **数据加密**: HTTPS传输，敏感数据加密存储
- **输入验证**: 防止XSS、SQL注入等攻击

### 2. 接口安全
- **身份验证**: 基于Token的身份验证
- **接口鉴权**: 细粒度的接口权限控制
- **频率限制**: 防止接口滥用

## 🚀 性能优化

### 1. 前端优化
- **组件懒加载**: 按需加载组件
- **数据缓存**: 合理使用缓存机制
- **代码分割**: 减少首屏加载时间

### 2. 数据优化
- **分页加载**: 大数据量分页处理
- **数据预加载**: 预加载关键数据
- **缓存策略**: 合理的缓存策略

## 🛠️ 调试技巧

### 1. 开发调试
```javascript
// 控制台输出
console.log('调试信息:', data);

// 断点调试
debugger;

// 错误捕获
try {
  // 可能出错的代码
} catch (error) {
  console.error('错误信息:', error);
}
```

### 2. 生产调试
- **日志记录**: 关键操作日志记录
- **错误监控**: 线上错误监控
- **性能监控**: 页面性能监控

## 📈 最佳实践

### 1. 代码规范
- **命名规范**: 使用有意义的变量和函数名
- **代码注释**: 关键逻辑添加注释
- **代码复用**: 提取公共逻辑为函数

### 2. 架构设计
- **模块化**: 按功能模块组织代码
- **解耦合**: 降低模块间的耦合度
- **可扩展**: 设计易于扩展的架构

### 3. 用户体验
- **响应速度**: 优化页面加载和响应速度
- **交互反馈**: 提供清晰的操作反馈
- **错误处理**: 友好的错误提示和处理

## 🎓 学习路径

### 初级开发者
1. 掌握基础组件使用
2. 理解数据绑定机制
3. 学会简单的事件处理

### 中级开发者
1. 掌握复杂组件配置
2. 理解生命周期管理
3. 学会API集成

### 高级开发者
1. 掌握自定义组件开发
2. 理解架构设计原则
3. 具备性能优化能力

### 专家级开发者
1. 深入理解底层原理
2. 具备复杂业务场景解决能力
3. 能够指导团队开发
