# 宜搭API完整参考指南

## 📋 API分类概览

### 1. 宜搭JS-API
用于在宜搭设计器中调用前端功能的API集合

### 2. 钉钉JS-API  
用于调用钉钉客户端原生能力的API集合

### 3. 跨应用数据源API
用于跨应用数据操作的HTTP API集合

### 4. 服务端开放API
用于服务端调用的RESTful API集合

## 🔧 宜搭JS-API详解

### 1. 状态管理API

#### 1.1 读取状态
```javascript
// 获取全局变量
const userName = this.state.userName;
const userInfo = this.state.userInfo;

// 获取URL参数
const id = this.state.urlParams.id;
const type = this.state.urlParams.type;

// 在变量绑定中使用（可省略this）
${state.userName}
${state.urlParams.id}
```

#### 1.2 更新状态
```javascript
// 更新单个变量
this.setState({
  userName: '张三'
});

// 更新多个变量
this.setState({
  userName: '张三',
  userAge: 25,
  userInfo: {
    name: '张三',
    age: 25,
    department: '技术部'
  }
});

// 基于当前状态更新
this.setState({
  count: this.state.count + 1,
  lastUpdateTime: new Date().toISOString()
});
```

### 2. 组件操作API

#### 2.1 获取组件值
```javascript
// 获取表单字段值
const name = this.$('nameField').getValue();
const email = this.$('emailField').getValue();
const selectedItems = this.$('selectField').getValue();

// 获取表格选中行
const selectedRows = this.$('dataTable').getSelectedRows();

// 获取复选框状态
const isChecked = this.$('checkboxField').getValue();
```

#### 2.2 设置组件值
```javascript
// 设置表单字段值
this.$('nameField').setValue('张三');
this.$('emailField').setValue('<EMAIL>');

// 设置下拉选择值
this.$('selectField').setValue(['option1', 'option2']);

// 设置表格数据源
this.$('dataTable').setDataSource([
  { id: 1, name: '张三', age: 25 },
  { id: 2, name: '李四', age: 30 }
]);
```

#### 2.3 组件显示控制
```javascript
// 显示/隐藏组件
this.$('submitButton').setVisible(true);
this.$('loadingSpinner').setVisible(false);

// 启用/禁用组件
this.$('submitButton').setDisabled(false);
this.$('editButton').setDisabled(true);

// 设置组件属性
this.$('textField').setProps({
  placeholder: '请输入姓名',
  required: true,
  maxLength: 50
});
```

#### 2.4 组件样式控制
```javascript
// 设置组件样式
this.$('errorMessage').setStyle({
  color: '#ff4d4f',
  fontSize: '14px',
  fontWeight: 'bold'
});

// 添加CSS类
this.$('warningBox').addClass('warning-style');

// 移除CSS类
this.$('successBox').removeClass('success-style');
```

### 3. 表单操作API

#### 3.1 表单数据获取
```javascript
// 获取整个表单数据
const formData = this.getFormData();
console.log('表单数据:', formData);

// 获取指定字段数据
const specificData = this.getFormData(['nameField', 'emailField']);

// 获取表单验证状态
const isValid = this.validateForm();
```

#### 3.2 表单验证API
```javascript
// 设置字段验证消息
this.$('emailField').setValidateMessage('邮箱格式不正确');

// 清除验证消息
this.$('emailField').clearValidateMessage();

// 手动触发验证
const isFieldValid = this.$('emailField').validate();

// 自定义验证规则
export function validateEmail() {
  const email = this.$('emailField').getValue();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    this.$('emailField').setValidateMessage('请输入有效的邮箱地址');
    return false;
  }
  
  this.$('emailField').clearValidateMessage();
  return true;
}
```

### 4. 数据源操作API

#### 4.1 远程API调用
```javascript
// 基本API调用
export async function loadUserData() {
  try {
    const result = await this.dataSourceMap.getUserList.load({
      page: 1,
      size: 10,
      keyword: this.state.searchKeyword
    });
    
    if (result && result.success) {
      this.setState({
        userList: result.data.list,
        total: result.data.total
      });
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
    this.utils.toast({
      type: 'error',
      title: '加载失败',
      message: error.message
    });
  }
}

// 带参数的API调用
export async function updateUserInfo() {
  const formData = this.getFormData();
  
  try {
    const result = await this.dataSourceMap.updateUser.load({
      id: this.state.currentUserId,
      ...formData
    });
    
    if (result && result.success) {
      this.utils.toast({
        type: 'success',
        title: '更新成功'
      });
      this.loadUserData(); // 重新加载数据
    }
  } catch (error) {
    this.handleApiError(error);
  }
}
```

#### 4.2 数据源配置
```javascript
// 动态配置数据源参数
export function setupDataSource() {
  // 配置请求头
  this.dataSourceMap.apiName.setHeaders({
    'Authorization': `Bearer ${this.state.token}`,
    'Content-Type': 'application/json'
  });
  
  // 配置默认参数
  this.dataSourceMap.apiName.setParams({
    timestamp: Date.now(),
    version: '1.0'
  });
}
```

### 5. 工具函数API

#### 5.1 消息提示
```javascript
// 成功提示
this.utils.toast({
  type: 'success',
  title: '操作成功',
  message: '数据已保存'
});

// 错误提示
this.utils.toast({
  type: 'error',
  title: '操作失败',
  message: '网络连接异常'
});

// 警告提示
this.utils.toast({
  type: 'warning',
  title: '注意',
  message: '数据将被永久删除'
});

// 信息提示
this.utils.toast({
  type: 'info',
  title: '提示',
  message: '请先选择要操作的数据'
});
```

#### 5.2 确认对话框
```javascript
// 确认对话框
export function confirmDelete() {
  this.utils.confirm({
    title: '确认删除',
    message: '删除后数据无法恢复，确定要删除吗？',
    onOk: () => {
      this.performDelete();
    },
    onCancel: () => {
      console.log('用户取消删除');
    }
  });
}
```

#### 5.3 字符串处理
```javascript
// 字符串格式化
const formatted = this.utils.formatString('Hello {name}, today is {date}', {
  name: '张三',
  date: '2024-01-01'
});

// 日期格式化
const dateStr = this.utils.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss');

// 数字格式化
const numberStr = this.utils.formatNumber(1234.56, 2); // "1,234.56"
```

## 📱 钉钉JS-API详解

### 1. 设备信息API

```javascript
// 获取设备信息
export function getDeviceInfo() {
  dd.device.base.getInterface({
    onSuccess: (result) => {
      console.log('设备信息:', result);
      this.setState({
        deviceInfo: result
      });
    },
    onFail: (error) => {
      console.error('获取设备信息失败:', error);
    }
  });
}

// 获取网络状态
export function getNetworkType() {
  dd.device.connection.getNetworkType({
    onSuccess: (result) => {
      console.log('网络类型:', result.networkType);
      this.setState({
        networkType: result.networkType
      });
    }
  });
}
```

### 2. 用户交互API

```javascript
// 原生弹框
export function showNativeAlert() {
  dd.device.notification.alert({
    message: '这是一个原生弹框',
    title: '提示',
    buttonName: '确定',
    onSuccess: () => {
      console.log('用户点击了确定');
    }
  });
}

// 原生确认框
export function showNativeConfirm() {
  dd.device.notification.confirm({
    message: '确定要执行此操作吗？',
    title: '确认',
    buttonLabels: ['取消', '确定'],
    onSuccess: (result) => {
      if (result.buttonIndex === 1) {
        console.log('用户点击了确定');
        this.performAction();
      }
    }
  });
}

// 显示加载中
export function showLoading() {
  dd.device.notification.showPreloader({
    text: '加载中...',
    showIcon: true
  });
}

// 隐藏加载中
export function hideLoading() {
  dd.device.notification.hidePreloader();
}
```

### 3. 扫码API

```javascript
// 扫描二维码
export function scanQRCode() {
  dd.biz.util.scan({
    type: 'qr', // qr: 二维码, bar: 条形码
    onSuccess: (result) => {
      console.log('扫码结果:', result.text);
      this.setState({
        scanResult: result.text
      });
      this.processScanResult(result.text);
    },
    onFail: (error) => {
      console.error('扫码失败:', error);
    }
  });
}
```

### 4. 地理位置API

```javascript
// 获取当前位置
export function getCurrentLocation() {
  dd.device.geolocation.get({
    targetAccuracy: 200,
    coordinate: 1, // 0: 火星坐标, 1: 地球坐标
    withReGeocode: true,
    useCache: true,
    onSuccess: (result) => {
      console.log('位置信息:', result);
      this.setState({
        location: {
          latitude: result.latitude,
          longitude: result.longitude,
          address: result.address
        }
      });
    },
    onFail: (error) => {
      console.error('获取位置失败:', error);
    }
  });
}
```

## 🌐 跨应用数据源API

### 1. 表单数据操作

#### 1.1 查询表单数据
```javascript
// API路径: /dingtalk/web/{appCode}/v1/form/searchFormDatas.json
// 方法: POST

const searchParams = {
  formUuid: 'FORM-XXX',
  searchFieldJson: JSON.stringify({
    textField_xxx: '搜索关键词',
    numberField_xxx: {
      type: 'between',
      value: [1, 100]
    }
  }),
  pageSize: 20,
  currentPage: 1,
  sortFieldJson: JSON.stringify({
    createTimeGMT: 'desc'
  })
};

// 在数据源中配置此API，然后调用
const result = await this.dataSourceMap.searchFormData.load(searchParams);
```

#### 1.2 创建表单数据
```javascript
// API路径: /dingtalk/web/{appCode}/v1/form/saveFormData.json
// 方法: POST

const formData = {
  formUuid: 'FORM-XXX',
  formDataJson: JSON.stringify({
    textField_xxx: '文本内容',
    numberField_xxx: 123,
    selectField_xxx: 'option1',
    employeeField_xxx: ['userId1', 'userId2']
  })
};

const result = await this.dataSourceMap.createFormData.load(formData);
```

#### 1.3 更新表单数据
```javascript
// API路径: /dingtalk/web/{appCode}/v1/form/updateFormData.json
// 方法: POST

const updateData = {
  formUuid: 'FORM-XXX',
  formInstId: 'FINST-XXX',
  updateFormDataJson: JSON.stringify({
    textField_xxx: '更新后的文本',
    numberField_xxx: 456
  })
};

const result = await this.dataSourceMap.updateFormData.load(updateData);
```

### 2. 流程操作API

#### 2.1 发起流程
```javascript
// API路径: /dingtalk/web/{appCode}/v1/process/startInstance.json
// 方法: POST

const processData = {
  processCode: 'PROC-XXX',
  formDataJson: JSON.stringify({
    textField_xxx: '申请内容',
    numberField_xxx: 1000
  }),
  departmentId: 'dept123',
  originatorUserId: 'user123'
};

const result = await this.dataSourceMap.startProcess.load(processData);
```

#### 2.2 查询流程实例
```javascript
// API路径: /dingtalk/web/{appCode}/v1/process/listProcessInstances.json
// 方法: POST

const queryParams = {
  processCode: 'PROC-XXX',
  startTime: '2024-01-01 00:00:00',
  endTime: '2024-12-31 23:59:59',
  pageSize: 20,
  pageNumber: 1
};

const result = await this.dataSourceMap.queryProcesses.load(queryParams);
```

## 🔐 服务端开放API

### 1. 认证授权

```javascript
// 获取访问令牌
const getAccessToken = async () => {
  const response = await fetch('https://oapi.dingtalk.com/gettoken', {
    method: 'GET',
    params: {
      appkey: 'your_app_key',
      appsecret: 'your_app_secret'
    }
  });
  
  const result = await response.json();
  return result.access_token;
};
```

### 2. 表单数据操作

```javascript
// 服务端查询表单数据
const searchFormData = async (accessToken, appType, systemToken, userId, params) => {
  const response = await fetch('https://oapi.dingtalk.com/topapi/yida/form/searchformdata', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      access_token: accessToken,
      app_type: appType,
      system_token: systemToken,
      user_id: userId,
      form_uuid: params.formUuid,
      search_field_json: params.searchFieldJson,
      page_size: params.pageSize,
      current_page: params.currentPage
    })
  });
  
  return await response.json();
};
```

## 🎯 API使用最佳实践

### 1. 错误处理

```javascript
// 统一的API错误处理
export async function callApiWithErrorHandling(apiCall, errorContext = '') {
  try {
    const result = await apiCall();
    
    // 检查业务错误
    if (result && !result.success) {
      throw new Error(result.message || '业务处理失败');
    }
    
    return result;
  } catch (error) {
    console.error(`API调用失败 [${errorContext}]:`, error);
    
    // 根据错误类型进行不同处理
    if (error.name === 'NetworkError') {
      this.utils.toast({
        type: 'error',
        title: '网络错误',
        message: '请检查网络连接后重试'
      });
    } else if (error.message.includes('权限')) {
      this.utils.toast({
        type: 'error',
        title: '权限不足',
        message: '您没有执行此操作的权限'
      });
    } else {
      this.utils.toast({
        type: 'error',
        title: '操作失败',
        message: error.message || '系统异常，请稍后重试'
      });
    }
    
    throw error;
  }
}
```

### 2. API缓存策略

```javascript
// API结果缓存
const ApiCache = {
  cache: new Map(),
  
  // 带缓存的API调用
  async callWithCache(cacheKey, apiCall, ttl = 300000) {
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }
    
    const result = await apiCall();
    this.cache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });
    
    return result;
  },
  
  // 清除缓存
  clearCache(cacheKey) {
    if (cacheKey) {
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }
};
```

### 3. 批量操作优化

```javascript
// 批量API调用
export async function batchApiCall(apiCalls, concurrency = 3) {
  const results = [];
  
  for (let i = 0; i < apiCalls.length; i += concurrency) {
    const batch = apiCalls.slice(i, i + concurrency);
    const batchResults = await Promise.allSettled(
      batch.map(call => call())
    );
    
    results.push(...batchResults);
  }
  
  return results;
}
```

这个API参考指南为AI系统提供了宜搭平台所有API的详细使用方法和最佳实践。
