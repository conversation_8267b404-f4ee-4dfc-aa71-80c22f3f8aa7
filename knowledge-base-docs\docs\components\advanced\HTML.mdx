---
title: HTML
order: 2
---

# HTML

HTML 组件允许你进行一些自定义的功能开发。其中页面设计需要有一定的前端基础，至少需要掌握 HTML、CSS、JS 等相关的基础知识。

- [HTML 教程](https://developer.mozilla.org/zh-CN/docs/Web/HTML)
- [CSS 教程](https://developer.mozilla.org/zh-CN/docs/Web/CSS)
- [JavaScript 教程](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript)

## 何时使用

- 用于在当前页面编辑代码块，实现比较复杂的页面样式。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/html-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'content',
      type: 'string',
      default: `'<div>这里可以写html</div>'`,
      desc: '用来编写需要展示的HTML',
    },
  ]}
/>
