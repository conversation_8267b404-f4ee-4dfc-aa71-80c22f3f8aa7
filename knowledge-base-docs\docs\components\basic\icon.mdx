---
title: Icon 图标
order: 1
---

# Icon 图标

图标展示，用户可以使用宜搭内置的图标，并设置其尺寸。

## 何时使用

通过图标来展示一些标记，例如警告、提示、趋势等。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/icon-v2?isRenderNav=false" />

## 自定义图标使用
   ### 1.需要先进入<a href="https://www.iconfont.cn/">iconfont</a>
   ### 2.添加喜欢的图标到购物车，添加完成后进入购物车，把icon添加到新项目中（推荐新建项目）
   ### 3.选择Symbol，查看在线链接，复制代码到宜搭；：示例：
   ```
    //at.alicdn.com/t/font_3429645_pimqzpqqzps.js
   ```
   ### 4.复制icon的name到宜搭：示例：
   ```
    icon-liebiao
   ```

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'baseType',
      type: 'string',
      default: ` 'smile' `,
      desc: '基础图标，只对应上面示例中icon下的文案',
    },
    {
      code: 'otherType',
      type: 'string',
      default: ` 'smile' `,
      desc: '自定义图标',
    },
    {
      code: 'useType',
      type: 'boolean',
      default: 'false',
      desc: '图标使用类型，若为true则 **otherType** 属性生效，若为false则 **baseType** 属性生效',
    },
    {
      code: 'size',
      type: `'xxs' | 'xs' | 'small' | 'medium' | 'large' | 'xl' |'xxl' | 'xxxl'`,
      default: ` 'medium' `,
      desc: '图标尺寸大小',
    },
  ]}
/>
