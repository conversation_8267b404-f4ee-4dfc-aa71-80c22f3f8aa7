# 整理项目中的所有 Markdown 文件到独立文件夹
# 排除 node_modules 文件夹中的文件

# 创建目标文件夹
$targetDir = ".\knowledge-base-docs"
if (Test-Path $targetDir) {
    Remove-Item $targetDir -Recurse -Force
}
New-Item -ItemType Directory -Path $targetDir -Force

# 获取所有 MD 文件，排除 node_modules
$mdFiles = Get-ChildItem -Path . -Recurse -Include "*.md","*.mdx" | Where-Object { $_.FullName -notlike "*node_modules*" }

Write-Host "找到 $($mdFiles.Count) 个 Markdown 文件"

# 复制文件并保持目录结构
foreach ($file in $mdFiles) {
    # 获取相对路径
    $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart("\")
    
    # 创建目标路径
    $targetPath = Join-Path $targetDir $relativePath
    $targetFolder = Split-Path $targetPath -Parent
    
    # 确保目标文件夹存在
    if (!(Test-Path $targetFolder)) {
        New-Item -ItemType Directory -Path $targetFolder -Force | Out-Null
    }
    
    # 复制文件
    Copy-Item $file.FullName $targetPath -Force
    Write-Host "已复制: $relativePath"
}

Write-Host "`n整理完成！所有文档已保存到 '$targetDir' 文件夹中"
Write-Host "文件夹结构已保持原有的组织方式，方便 AI 理解文档的层次结构"

# 创建一个索引文件
$indexPath = Join-Path $targetDir "INDEX.md"
$indexContent = "# 宜搭开发者中心 - 知识库文档`n`n"
$indexContent += "这个文件夹包含了宜搭开发者中心项目的所有 Markdown 文档，按原有的目录结构组织。`n`n"
$indexContent += "## 文档结构`n`n"
$indexContent += "### 主要文档`n"
$indexContent += "- README.md - 项目主要说明文档`n`n"
$indexContent += "### docs/ 目录`n"
$indexContent += "- api/ - API 相关文档`n"
$indexContent += "- components/ - 组件文档`n"
$indexContent += "- guide/ - 使用指南`n"
$indexContent += "- tutorial/ - 教程`n"
$indexContent += "- usage/ - 使用说明`n`n"
$indexContent += "## 使用说明`n`n"
$indexContent += "这些文档可以作为 AI 知识库使用，包含了：`n"
$indexContent += "1. 宜搭平台的完整介绍和使用指南`n"
$indexContent += "2. API 接口文档和使用方法`n"
$indexContent += "3. 组件库的详细说明`n"
$indexContent += "4. 开发教程和最佳实践`n"
$indexContent += "5. 常见问题解答`n`n"
$indexContent += "文档采用 Markdown 格式，便于阅读和处理。`n"

Set-Content -Path $indexPath -Value $indexContent -Encoding UTF8
Write-Host "已创建索引文件: INDEX.md"
