---
title: CascadeSelectField 级联选择
order: 15
---

# CascadeSelectField 级联选择

## 何时使用

- 级联选择由选择器和级联组成。把级联组件以弹层的方式隐藏，多用于表单场景。

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/cascade-select-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'string',
      default: `'part_b'`,
      desc: '数据源中最后一个节点的 value',
    },
    {
      code: 'columnsNum',
      type: 'number',
      default: '0',
      desc: '级联层级',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请选择'`,
      desc: '占位提示',
    },
    {
      code: 'listStyle',
      type: 'React.CSSProperties',
      default: '{}',
      desc: '列表样式',
    },
    {
      code: 'expandTriggerType',
      type: `'click' | 'hover'`,
      default: 'click',
      desc: '触发行为',
    },
    {
      code: 'hasArrow',
      type: 'boolean',
      default: 'true',
      desc: '是否显示下拉箭头',
    },
    {
      code: 'hasBorder',
      type: 'boolean',
      default: 'true',
      desc: '是否显示边框',
    },
    {
      code: 'showSearch',
      type: 'boolean',
      default: 'false',
      desc: '是否显示搜索框',
    },
    {
      code: 'hasClear',
      type: 'boolean',
      default: 'true',
      desc: '是否显示清除按钮',
    },
    {
      code: 'dataSource',
      type: '[CascadeDataSource[]](/docs/components/interface#cascadeDataSource)',
      default: `~~~json
[{
		"value": "part",
		"label": {
			"type": "i18n",
			"en_US": "dep",
			"zh_CN": "部门"
		},
		"children": [{
				"value": "part_a",
				"label": "A部门"
			},
			{
				"value": "part_b",
				"label": "B部门"
			}
		]
	},
	{
		"value": "product",
		"label": "产品",
		"children": [{
				"value": "product_a",
				"label": "a产品"
			},
			{
				"value": "product_b",
				"label": "b产品"
			}
		]
	}
]`,
      desc: '数据源',
    },
    {
      code: 'isLoadData',
      type: 'boolean',
      default: 'false',
      desc: '是否开启异步加载',
    },
    {
      code: 'loadData',
      type: '( node: object ) => Promise<CascadeDataSource[]>',
      default: '-',
      desc: 'loadData 函数',
    },
    {
      code: 'notFoundContent',
      type: 'string',
      default: `'无数据'`,
      desc: '无数据时显示内容',
    },
    {
      code: 'optionAutoWidth',
      type: 'boolean',
      default: 'false',
      desc: '是否开启选项自动宽度，仅支持PC端',
    },
    {
      code: 'onChange',
      type: '({ value: string | array, data: object | array , extra: object }) => void',
      default: '-',
      desc: 'onChange 值发生变化',
    },
    {
      code: 'onVisibleChange',
      type: '( visible : boolean ) => void',
      default: '-',
      desc: 'onVisibleChange 弹层显示隐藏变化',
    },
  ]}
/>
