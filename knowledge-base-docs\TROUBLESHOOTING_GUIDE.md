# 宜搭开发问题排查与解决方案

## 🚨 常见问题分类

### 1. 开发环境问题

#### 1.1 设计器无法正常加载
**问题现象**：
- 设计器页面白屏或加载失败
- 组件面板无法显示
- 属性面板配置无效

**排查步骤**：
```javascript
// 1. 检查浏览器控制台错误
console.log('检查控制台错误信息');

// 2. 检查网络请求状态
// 在Network面板查看是否有请求失败

// 3. 清除浏览器缓存
// Ctrl+Shift+Delete 清除缓存

// 4. 检查浏览器兼容性
const isSupported = () => {
  return window.fetch && window.Promise && window.Map;
};
```

**解决方案**：
1. 使用Chrome、Edge等现代浏览器
2. 清除浏览器缓存和Cookie
3. 检查网络连接状态
4. 联系管理员检查权限配置

#### 1.2 组件拖拽失效
**问题现象**：
- 无法从组件面板拖拽组件到画布
- 拖拽后组件不显示
- 组件位置异常

**解决方案**：
```javascript
// 检查画布容器状态
export function checkCanvasStatus() {
  const canvas = document.querySelector('.canvas-container');
  if (!canvas) {
    console.error('画布容器未找到');
    return false;
  }
  
  // 检查容器样式
  const styles = window.getComputedStyle(canvas);
  if (styles.position === 'static') {
    console.warn('画布容器需要设置position: relative');
  }
  
  return true;
}
```

### 2. 数据绑定问题

#### 2.1 数据绑定不生效
**问题现象**：
- 组件显示${state.xxx}而不是实际值
- 数据更新后界面不刷新
- 表达式计算错误

**排查方法**：
```javascript
// 1. 检查变量名是否正确
export function checkVariableName() {
  console.log('当前状态:', this.state);
  console.log('可用变量:', Object.keys(this.state));
}

// 2. 检查数据类型
export function checkDataType() {
  const data = this.state.someData;
  console.log('数据类型:', typeof data);
  console.log('数据值:', data);
  console.log('是否为数组:', Array.isArray(data));
}

// 3. 检查表达式语法
export function validateExpression() {
  try {
    // 测试表达式是否能正常执行
    const result = eval('this.state.userName || "默认值"');
    console.log('表达式结果:', result);
  } catch (error) {
    console.error('表达式语法错误:', error);
  }
}
```

**解决方案**：
1. 确保变量名拼写正确
2. 检查数据类型匹配
3. 使用正确的表达式语法
4. 避免在表达式中使用复杂逻辑

#### 2.2 远程数据源调用失败
**问题现象**：
- API请求返回错误
- 数据格式不匹配
- 跨域问题

**调试代码**：
```javascript
// API调用调试
export async function debugApiCall() {
  try {
    console.log('开始调用API...');
    
    const result = await this.dataSourceMap.testApi.load({
      param1: 'test'
    });
    
    console.log('API响应:', result);
    console.log('响应类型:', typeof result);
    console.log('是否成功:', result && result.success);
    
  } catch (error) {
    console.error('API调用失败:', error);
    console.error('错误类型:', error.name);
    console.error('错误信息:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

// 检查API配置
export function checkApiConfig() {
  const apiConfig = this.dataSourceMap.testApi;
  console.log('API配置:', {
    url: apiConfig.url,
    method: apiConfig.method,
    headers: apiConfig.headers,
    params: apiConfig.params
  });
}
```

### 3. 组件行为问题

#### 3.1 表单验证不生效
**问题现象**：
- 必填验证不触发
- 自定义验证规则无效
- 验证错误信息不显示

**解决方案**：
```javascript
// 手动触发表单验证
export function validateForm() {
  const formFields = [
    'nameField',
    'emailField',
    'phoneField'
  ];
  
  let isValid = true;
  const errors = {};
  
  formFields.forEach(fieldId => {
    const field = this.$(fieldId);
    const value = field.getValue();
    
    // 检查必填
    if (field.getProps().required && !value) {
      errors[fieldId] = '此字段为必填项';
      isValid = false;
    }
    
    // 自定义验证
    if (fieldId === 'emailField' && value && !this.isValidEmail(value)) {
      errors[fieldId] = '请输入有效的邮箱地址';
      isValid = false;
    }
  });
  
  // 显示验证错误
  Object.keys(errors).forEach(fieldId => {
    this.$(fieldId).setValidateMessage(errors[fieldId]);
  });
  
  return isValid;
}

// 邮箱验证函数
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

#### 3.2 组件事件不触发
**问题现象**：
- 按钮点击无响应
- 表单提交事件不执行
- 自定义事件不生效

**调试方法**：
```javascript
// 事件绑定检查
export function checkEventBinding() {
  const button = this.$('submitButton');
  
  // 检查事件是否正确绑定
  console.log('按钮配置:', button.getProps());
  console.log('事件处理器:', button.getProps().onClick);
  
  // 手动绑定事件
  button.on('click', () => {
    console.log('按钮被点击');
    this.handleSubmit();
  });
}

// 事件处理函数
export function handleSubmit() {
  console.log('处理表单提交');
  
  // 检查表单数据
  const formData = this.getFormData();
  console.log('表单数据:', formData);
  
  // 执行提交逻辑
  this.submitForm(formData);
}
```

### 4. 性能问题

#### 4.1 页面加载缓慢
**问题现象**：
- 页面首次加载时间过长
- 组件渲染卡顿
- 数据加载延迟

**性能优化**：
```javascript
// 性能监控
export function performanceMonitor() {
  // 记录页面加载时间
  const startTime = performance.now();
  
  // 页面加载完成后
  window.addEventListener('load', () => {
    const loadTime = performance.now() - startTime;
    console.log('页面加载时间:', loadTime + 'ms');
  });
  
  // 监控API调用时间
  const originalLoad = this.dataSourceMap.getData.load;
  this.dataSourceMap.getData.load = async function(...args) {
    const apiStartTime = performance.now();
    const result = await originalLoad.apply(this, args);
    const apiEndTime = performance.now();
    console.log('API调用时间:', (apiEndTime - apiStartTime) + 'ms');
    return result;
  };
}

// 数据分页加载
export function setupPagination() {
  this.setState({
    currentPage: 1,
    pageSize: 20, // 减少单页数据量
    total: 0
  });
  
  // 加载数据
  this.loadPageData();
}

export async function loadPageData() {
  const { currentPage, pageSize } = this.state;
  
  try {
    this.setState({ loading: true });
    
    const result = await this.dataSourceMap.getPageData.load({
      page: currentPage,
      size: pageSize
    });
    
    if (result.success) {
      this.setState({
        dataList: result.data.list,
        total: result.data.total
      });
    }
  } catch (error) {
    console.error('数据加载失败:', error);
  } finally {
    this.setState({ loading: false });
  }
}
```

#### 4.2 内存泄漏问题
**问题现象**：
- 页面使用时间长后变卡
- 浏览器内存占用持续增长
- 页面崩溃

**解决方案**：
```javascript
// 清理定时器
export function didMount() {
  // 设置定时器
  this.timer = setInterval(() => {
    this.refreshData();
  }, 30000);
  
  // 设置清理函数
  this.cleanup = () => {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  };
}

export function willUnmount() {
  // 页面销毁时清理资源
  if (this.cleanup) {
    this.cleanup();
  }
  
  // 清理事件监听
  if (this.eventListeners) {
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
  }
}

// 避免闭包引用
export function setupEventHandler() {
  const handler = (event) => {
    // 避免在事件处理器中引用大对象
    const data = event.target.value;
    this.handleDataChange(data);
  };
  
  // 记录事件监听器以便清理
  this.eventListeners = this.eventListeners || [];
  this.eventListeners.push({
    element: document,
    event: 'change',
    handler
  });
  
  document.addEventListener('change', handler);
}
```

### 5. 兼容性问题

#### 5.1 浏览器兼容性
**问题现象**：
- 在某些浏览器中功能异常
- 样式显示不一致
- JavaScript错误

**兼容性检查**：
```javascript
// 浏览器特性检测
export function checkBrowserSupport() {
  const features = {
    fetch: typeof fetch !== 'undefined',
    promise: typeof Promise !== 'undefined',
    arrow: (() => { try { eval('() => {}'); return true; } catch(e) { return false; } })(),
    const: (() => { try { eval('const a = 1'); return true; } catch(e) { return false; } })(),
    flexbox: CSS.supports('display', 'flex'),
    grid: CSS.supports('display', 'grid')
  };
  
  console.log('浏览器特性支持:', features);
  
  // 检查不支持的特性
  const unsupported = Object.keys(features).filter(key => !features[key]);
  if (unsupported.length > 0) {
    console.warn('不支持的特性:', unsupported);
  }
  
  return unsupported.length === 0;
}

// Polyfill加载
export function loadPolyfills() {
  // 检查并加载必要的polyfill
  if (!window.fetch) {
    // 加载fetch polyfill
    const script = document.createElement('script');
    script.src = 'https://polyfill.io/v3/polyfill.min.js?features=fetch';
    document.head.appendChild(script);
  }
}
```

#### 5.2 移动端适配问题
**问题现象**：
- 在移动设备上显示异常
- 触摸事件不响应
- 布局错乱

**移动端优化**：
```javascript
// 移动端检测
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 移动端适配
export function setupMobileAdaptation() {
  if (this.isMobile()) {
    // 设置视口
    const viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.head.appendChild(meta);
    }
    
    // 添加移动端样式类
    document.body.classList.add('mobile-device');
    
    // 优化触摸事件
    this.setupTouchEvents();
  }
}

export function setupTouchEvents() {
  // 防止双击缩放
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (event) => {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
      event.preventDefault();
    }
    lastTouchEnd = now;
  }, false);
  
  // 优化滚动性能
  document.addEventListener('touchmove', (event) => {
    if (event.target.closest('.scrollable')) {
      return;
    }
    event.preventDefault();
  }, { passive: false });
}
```

## 🔧 调试工具和技巧

### 1. 开发者工具使用

```javascript
// 调试工具集
const DebugTools = {
  // 状态查看器
  inspectState() {
    console.group('当前状态');
    console.log('全局状态:', this.state);
    console.log('组件状态:', this.getComponentStates());
    console.groupEnd();
  },
  
  // 组件查看器
  inspectComponent(componentId) {
    const component = this.$(componentId);
    if (component) {
      console.group(`组件 ${componentId}`);
      console.log('属性:', component.getProps());
      console.log('值:', component.getValue());
      console.log('可见性:', component.getVisible());
      console.groupEnd();
    } else {
      console.error(`组件 ${componentId} 不存在`);
    }
  },
  
  // API调用日志
  logApiCalls() {
    const originalLoad = this.dataSourceMap.load;
    this.dataSourceMap.load = function(apiName, params) {
      console.log(`API调用: ${apiName}`, params);
      const startTime = Date.now();
      
      return originalLoad.call(this, apiName, params)
        .then(result => {
          console.log(`API响应: ${apiName} (${Date.now() - startTime}ms)`, result);
          return result;
        })
        .catch(error => {
          console.error(`API错误: ${apiName}`, error);
          throw error;
        });
    };
  }
};
```

### 2. 错误处理最佳实践

```javascript
// 全局错误处理
export function setupGlobalErrorHandler() {
  // 捕获未处理的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise错误:', event.reason);
    
    // 显示用户友好的错误信息
    this.utils.toast({
      type: 'error',
      title: '操作失败',
      message: '系统出现异常，请稍后重试'
    });
    
    // 阻止默认的错误处理
    event.preventDefault();
  });
  
  // 捕获JavaScript错误
  window.addEventListener('error', (event) => {
    console.error('JavaScript错误:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    });
  });
}

// 业务错误处理
export function handleBusinessError(error, context = '') {
  const errorInfo = {
    message: error.message || '未知错误',
    code: error.code || 'UNKNOWN_ERROR',
    context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  };
  
  console.error('业务错误:', errorInfo);
  
  // 根据错误类型显示不同的提示
  let userMessage = '操作失败，请稍后重试';
  
  switch (error.code) {
    case 'NETWORK_ERROR':
      userMessage = '网络连接异常，请检查网络后重试';
      break;
    case 'PERMISSION_DENIED':
      userMessage = '权限不足，请联系管理员';
      break;
    case 'DATA_VALIDATION_ERROR':
      userMessage = error.message || '数据验证失败';
      break;
  }
  
  this.utils.toast({
    type: 'error',
    title: '操作失败',
    message: userMessage
  });
}
```

这个问题排查指南为AI系统提供了全面的问题诊断和解决方案，帮助快速定位和解决宜搭开发中的各种问题。
