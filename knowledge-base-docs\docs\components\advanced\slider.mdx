---
title: Slider 轮播图
order: 6
---

# Slider 轮播图

轮播组件，就是以幻灯片的方式，在页面中横向展示诸多内容的组件。

## 何时使用

- 轮播内容相互独立，前后在内容以及数据上都不存在逻辑关系。
- 单图轮播：该样式通常用于承载运营 banner，是一个位置相对固定的模块。
- 多图轮播：单元信息浏览。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/slider-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'isDiy',
      type: 'boolean',
      default: 'false',
      desc: '用于开启自定义模式',
    },
    {
      code: 'diyContents',
      type: 'any[]',
      default: '[]',
      desc: '用于输入自定义模式数据，仅**isDiy**设置为true时生效',
    },
    {
      code: 'diyContentsRender',
      type: '(item: any, index: number) => ReactNode',
      default: '',
      desc: '用于渲染自定义模式渲染函数，仅**isDiy**设置为true时生效',
    },
    {
      code: 'images',
      type: '[SliderDataSource[]](/docs/components/interface#sliderdatasource)',
      default: '[]',
      desc: '用于配置轮播项',
    },
    {
      code: 'type',
      type: `'single' | 'multi'`,
      default: `'single'`,
      desc: '用于配置轮播方式，single：单项轮播、multi：多项轮播',
    },
    {
      code: 'slideImageWidth',
      type: 'string',
      default: `'100%'`,
      desc: '用于设置轮播项宽度',
    },
    {
      code: 'slideImageHeightAuto',
      type: 'boolean',
      default: 'false',
      desc: '开启轮播项高度自适应（图片时会保持长宽比）',
    },
    {
      code: 'slideImageHeight',
      type: 'string',
      default: `'300px'`,
      desc: '轮播图高度,单位为px或设置%',
    },
    {
      code: 'margin',
      type: 'number',
      default: '10',
      desc: '用于控制轮播项间距',
    },
    {
      code: 'slidesToShow',
      type: 'number',
      default: '2',
      desc: '用于控制同时展示的轮播项数量，当**type** 属性为multi时生效',
    },
    {
      code: 'slidesToScroll',
      type: 'number',
      default: '1',
      desc: '用于控制同时滑动的轮播项数量',
    },
    {
      code: 'centerMode',
      type: 'boolean',
      default: 'false',
      desc: '是否开启居中模式',
    },
    {
      code: 'focusOnSelect',
      type: 'boolean',
      default: 'false',
      desc: '用于多图轮播时，点击选中后自动居中',
    },
    {
      code: 'slideDirection',
      type: " 'hoz' | 'ver' ",
      default: "'hoz'",
      desc: '用于控制轮播方向',
    },
    {
      code: 'speed',
      type: 'number',
      default: '500',
      desc: '轮播速度，单位: ms',
    },
    {
      code: 'lazyLoad',
      type: 'boolean',
      default: 'false',
      desc: '是否开启懒加载模式',
    },
    {
      code: 'animation',
      type: " 'slide' | 'fade' ",
      default: `'slide'`,
      desc: '用于选择轮播项动效类型，slide：左右滑动、fade：渐变',
    },
    {
      code: 'arrows',
      type: 'boolean',
      default: 'true',
      desc: '是否开启轮播项箭头',
    },
    {
      code: 'arrowSize',
      type: " 'medium' | 'large' ",
      default: "'medium'",
      desc: '用于控制箭头大小',
    },
    {
      code: 'arrowPosition',
      type: " 'inner' | 'outer' ",
      default: "'inner'",
      desc: '用于控制箭头位置',
    },
    {
      code: 'arrowDirection ',
      type: "'hoz' |'ver' ",
      default: "'hoz'",
      desc: '控制箭头展示方向',
    },
    {
      code: 'autoplay',
      type: 'boolean',
      default: 'false',
      desc: '用于选择是否自动播放轮播项',
    },
    {
      code: 'autoplaySpeed',
      type: 'number',
      default: '3000',
      desc: '轮播项自动播放速度，单位：ms',
    },
    {
      code: 'dots',
      type: 'boolean',
      default: 'true',
      desc: '是否开启导航锚点',
    },
    {
      code: 'dotsDirection',
      type: " 'hoz' | 'ver' ",
      default: "'hoz'",
      desc: '导航锚点显示位置',
    },
    {
      code: 'triggerType',
      type: " 'click' | 'hover' ",
      default: "'click'",
      desc: '锚点导航触发方式',
    },
  ]}
/>

