# 宜搭项目实战案例集

## 🎯 案例分类

### 1. 企业管理系统
- 员工管理系统
- 项目管理系统  
- 客户关系管理(CRM)
- 库存管理系统

### 2. 业务流程系统
- 请假审批系统
- 报销管理系统
- 采购申请系统
- 合同审批系统

### 3. 数据展示系统
- 销售数据看板
- 运营监控面板
- 财务报表系统
- 设备监控系统

## 📊 案例1：员工管理系统

### 系统架构
```
员工管理系统
├── 员工信息管理
│   ├── 员工列表页面
│   ├── 员工详情页面
│   ├── 新增员工页面
│   └── 编辑员工页面
├── 部门管理
│   ├── 部门树形结构
│   └── 部门人员分配
└── 权限管理
    ├── 角色定义
    └── 权限分配
```

### 核心功能实现

#### 1.1 员工列表页面
```javascript
// 页面初始化
export function didMount() {
  this.initializePage();
  this.loadEmployeeList();
  this.loadDepartmentOptions();
}

// 初始化页面状态
export function initializePage() {
  this.setState({
    employeeList: [],
    departmentList: [],
    searchParams: {
      keyword: '',
      departmentId: '',
      status: 'active'
    },
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    },
    loading: false,
    selectedRows: []
  });
}

// 加载员工列表
export async function loadEmployeeList() {
  const { searchParams, pagination } = this.state;
  
  try {
    this.setState({ loading: true });
    
    const result = await this.dataSourceMap.getEmployeeList.load({
      ...searchParams,
      page: pagination.current,
      size: pagination.pageSize
    });
    
    if (result && result.success) {
      this.setState({
        employeeList: result.data.list,
        pagination: {
          ...pagination,
          total: result.data.total
        }
      });
      
      // 更新表格数据
      this.$('employeeTable').setDataSource(result.data.list);
    }
  } catch (error) {
    this.handleError(error, '加载员工列表');
  } finally {
    this.setState({ loading: false });
  }
}

// 搜索功能
export function onSearch() {
  const keyword = this.$('searchInput').getValue();
  const departmentId = this.$('departmentSelect').getValue();
  
  this.setState({
    searchParams: {
      ...this.state.searchParams,
      keyword,
      departmentId
    },
    pagination: {
      ...this.state.pagination,
      current: 1
    }
  });
  
  this.loadEmployeeList();
}

// 新增员工
export function onAddEmployee() {
  // 跳转到新增页面
  this.utils.router.push('/employee/add');
}

// 编辑员工
export function onEditEmployee(record) {
  // 跳转到编辑页面
  this.utils.router.push(`/employee/edit/${record.id}`);
}

// 删除员工
export function onDeleteEmployee(record) {
  this.utils.confirm({
    title: '确认删除',
    message: `确定要删除员工 ${record.name} 吗？`,
    onOk: async () => {
      try {
        const result = await this.dataSourceMap.deleteEmployee.load({
          id: record.id
        });
        
        if (result && result.success) {
          this.utils.toast({
            type: 'success',
            title: '删除成功'
          });
          this.loadEmployeeList();
        }
      } catch (error) {
        this.handleError(error, '删除员工');
      }
    }
  });
}

// 批量操作
export function onBatchOperation(action) {
  const { selectedRows } = this.state;
  
  if (selectedRows.length === 0) {
    this.utils.toast({
      type: 'warning',
      title: '请先选择要操作的员工'
    });
    return;
  }
  
  switch (action) {
    case 'export':
      this.exportEmployees(selectedRows);
      break;
    case 'disable':
      this.batchDisableEmployees(selectedRows);
      break;
  }
}
```

#### 1.2 员工表单组件
```javascript
// 员工表单验证
export function validateEmployeeForm() {
  const formData = this.getFormData();
  const errors = {};
  
  // 姓名验证
  if (!formData.name || formData.name.trim().length === 0) {
    errors.name = '请输入员工姓名';
  } else if (formData.name.length > 20) {
    errors.name = '姓名长度不能超过20个字符';
  }
  
  // 工号验证
  if (!formData.employeeNo) {
    errors.employeeNo = '请输入员工工号';
  } else if (!/^[A-Z0-9]{6,10}$/.test(formData.employeeNo)) {
    errors.employeeNo = '工号格式不正确，应为6-10位大写字母和数字';
  }
  
  // 邮箱验证
  if (!formData.email) {
    errors.email = '请输入邮箱地址';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = '邮箱格式不正确';
  }
  
  // 手机号验证
  if (!formData.phone) {
    errors.phone = '请输入手机号码';
  } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
    errors.phone = '手机号格式不正确';
  }
  
  // 部门验证
  if (!formData.departmentId) {
    errors.departmentId = '请选择所属部门';
  }
  
  // 职位验证
  if (!formData.position) {
    errors.position = '请输入职位';
  }
  
  // 显示验证错误
  Object.keys(errors).forEach(field => {
    this.$(`${field}Field`).setValidateMessage(errors[field]);
  });
  
  return Object.keys(errors).length === 0;
}

// 保存员工信息
export async function saveEmployee() {
  if (!this.validateEmployeeForm()) {
    return;
  }
  
  const formData = this.getFormData();
  const isEdit = !!this.state.currentEmployeeId;
  
  try {
    this.setState({ saving: true });
    
    const apiCall = isEdit ? 
      this.dataSourceMap.updateEmployee.load({
        id: this.state.currentEmployeeId,
        ...formData
      }) :
      this.dataSourceMap.createEmployee.load(formData);
    
    const result = await apiCall;
    
    if (result && result.success) {
      this.utils.toast({
        type: 'success',
        title: isEdit ? '更新成功' : '创建成功'
      });
      
      // 返回列表页面
      this.utils.router.push('/employee/list');
    }
  } catch (error) {
    this.handleError(error, isEdit ? '更新员工' : '创建员工');
  } finally {
    this.setState({ saving: false });
  }
}
```

## 📋 案例2：请假审批系统

### 系统设计
```
请假审批系统
├── 请假申请
│   ├── 申请表单
│   ├── 申请历史
│   └── 申请状态查询
├── 审批管理
│   ├── 待审批列表
│   ├── 审批历史
│   └── 审批统计
└── 系统设置
    ├── 请假类型配置
    ├── 审批流程配置
    └── 权限设置
```

### 核心功能实现

#### 2.1 请假申请表单
```javascript
// 请假申请表单初始化
export function didMount() {
  this.initializeLeaveForm();
  this.loadLeaveTypes();
  this.loadUserInfo();
}

export function initializeLeaveForm() {
  this.setState({
    leaveTypes: [],
    userInfo: {},
    formData: {
      leaveType: '',
      startDate: '',
      endDate: '',
      reason: '',
      urgency: 'normal'
    },
    calculatedDays: 0,
    submitting: false
  });
}

// 计算请假天数
export function calculateLeaveDays() {
  const startDate = this.$('startDateField').getValue();
  const endDate = this.$('endDateField').getValue();
  
  if (!startDate || !endDate) {
    this.setState({ calculatedDays: 0 });
    return;
  }
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (end < start) {
    this.$('endDateField').setValidateMessage('结束日期不能早于开始日期');
    return;
  }
  
  // 计算工作日天数（排除周末）
  let days = 0;
  const current = new Date(start);
  
  while (current <= end) {
    const dayOfWeek = current.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // 排除周日(0)和周六(6)
      days++;
    }
    current.setDate(current.getDate() + 1);
  }
  
  this.setState({ calculatedDays: days });
  this.$('leaveDaysField').setValue(days);
}

// 提交请假申请
export async function submitLeaveApplication() {
  if (!this.validateLeaveForm()) {
    return;
  }
  
  const formData = this.getFormData();
  
  try {
    this.setState({ submitting: true });
    
    const result = await this.dataSourceMap.submitLeaveApplication.load({
      ...formData,
      applicantId: this.state.userInfo.userId,
      applicationTime: new Date().toISOString(),
      status: 'pending'
    });
    
    if (result && result.success) {
      this.utils.toast({
        type: 'success',
        title: '申请提交成功',
        message: '您的请假申请已提交，请等待审批'
      });
      
      // 跳转到申请历史页面
      this.utils.router.push('/leave/history');
    }
  } catch (error) {
    this.handleError(error, '提交请假申请');
  } finally {
    this.setState({ submitting: false });
  }
}

// 请假表单验证
export function validateLeaveForm() {
  const formData = this.getFormData();
  const errors = {};
  
  if (!formData.leaveType) {
    errors.leaveType = '请选择请假类型';
  }
  
  if (!formData.startDate) {
    errors.startDate = '请选择开始日期';
  }
  
  if (!formData.endDate) {
    errors.endDate = '请选择结束日期';
  }
  
  if (!formData.reason || formData.reason.trim().length === 0) {
    errors.reason = '请输入请假原因';
  } else if (formData.reason.length > 500) {
    errors.reason = '请假原因不能超过500个字符';
  }
  
  if (this.state.calculatedDays <= 0) {
    errors.general = '请假天数必须大于0';
  }
  
  // 显示验证错误
  Object.keys(errors).forEach(field => {
    if (field === 'general') {
      this.utils.toast({
        type: 'error',
        title: '表单验证失败',
        message: errors[field]
      });
    } else {
      this.$(`${field}Field`).setValidateMessage(errors[field]);
    }
  });
  
  return Object.keys(errors).length === 0;
}
```

#### 2.2 审批管理功能
```javascript
// 审批列表加载
export async function loadApprovalList() {
  const { searchParams, pagination } = this.state;
  
  try {
    this.setState({ loading: true });
    
    const result = await this.dataSourceMap.getApprovalList.load({
      approverId: this.state.userInfo.userId,
      status: searchParams.status,
      startDate: searchParams.startDate,
      endDate: searchParams.endDate,
      page: pagination.current,
      size: pagination.pageSize
    });
    
    if (result && result.success) {
      this.setState({
        approvalList: result.data.list,
        pagination: {
          ...pagination,
          total: result.data.total
        }
      });
      
      this.$('approvalTable').setDataSource(result.data.list);
    }
  } catch (error) {
    this.handleError(error, '加载审批列表');
  } finally {
    this.setState({ loading: false });
  }
}

// 审批操作
export function onApprove(record, action) {
  const actionText = action === 'approve' ? '通过' : '拒绝';
  
  this.utils.confirm({
    title: `确认${actionText}`,
    message: `确定要${actionText}${record.applicantName}的请假申请吗？`,
    onOk: () => {
      this.showApprovalDialog(record, action);
    }
  });
}

// 显示审批对话框
export function showApprovalDialog(record, action) {
  this.setState({
    currentApproval: record,
    approvalAction: action,
    approvalComment: ''
  });
  
  this.$('approvalDialog').setVisible(true);
}

// 提交审批结果
export async function submitApproval() {
  const { currentApproval, approvalAction, approvalComment } = this.state;
  
  if (approvalAction === 'reject' && !approvalComment.trim()) {
    this.utils.toast({
      type: 'warning',
      title: '请输入拒绝原因'
    });
    return;
  }
  
  try {
    const result = await this.dataSourceMap.submitApproval.load({
      applicationId: currentApproval.id,
      action: approvalAction,
      comment: approvalComment,
      approverId: this.state.userInfo.userId,
      approvalTime: new Date().toISOString()
    });
    
    if (result && result.success) {
      this.utils.toast({
        type: 'success',
        title: '审批完成'
      });
      
      this.$('approvalDialog').setVisible(false);
      this.loadApprovalList();
    }
  } catch (error) {
    this.handleError(error, '提交审批');
  }
}
```

## 📈 案例3：销售数据看板

### 看板设计
```
销售数据看板
├── 关键指标卡片
│   ├── 今日销售额
│   ├── 本月销售额
│   ├── 销售目标完成率
│   └── 客户数量
├── 图表展示
│   ├── 销售趋势图
│   ├── 产品销售占比
│   ├── 区域销售分布
│   └── 销售人员排行
└── 数据筛选
    ├── 时间范围选择
    ├── 产品类别筛选
    └── 销售区域筛选
```

### 核心功能实现

#### 3.1 数据看板初始化
```javascript
// 看板初始化
export function didMount() {
  this.initializeDashboard();
  this.loadDashboardData();
  this.setupAutoRefresh();
}

export function initializeDashboard() {
  this.setState({
    kpiData: {
      todaySales: 0,
      monthSales: 0,
      targetCompletion: 0,
      customerCount: 0
    },
    chartData: {
      salesTrend: [],
      productDistribution: [],
      regionDistribution: [],
      salesRanking: []
    },
    filters: {
      dateRange: [
        this.utils.formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD'),
        this.utils.formatDate(new Date(), 'YYYY-MM-DD')
      ],
      productCategory: 'all',
      salesRegion: 'all'
    },
    loading: false,
    lastUpdateTime: null
  });
}

// 加载看板数据
export async function loadDashboardData() {
  const { filters } = this.state;
  
  try {
    this.setState({ loading: true });
    
    // 并行加载多个数据源
    const [kpiResult, trendResult, distributionResult, rankingResult] = await Promise.all([
      this.dataSourceMap.getKpiData.load(filters),
      this.dataSourceMap.getSalesTrend.load(filters),
      this.dataSourceMap.getDistributionData.load(filters),
      this.dataSourceMap.getSalesRanking.load(filters)
    ]);
    
    // 更新KPI数据
    if (kpiResult && kpiResult.success) {
      this.setState({
        kpiData: kpiResult.data
      });
      this.updateKpiCards(kpiResult.data);
    }
    
    // 更新图表数据
    if (trendResult && trendResult.success) {
      this.updateSalesTrendChart(trendResult.data);
    }
    
    if (distributionResult && distributionResult.success) {
      this.updateDistributionCharts(distributionResult.data);
    }
    
    if (rankingResult && rankingResult.success) {
      this.updateRankingChart(rankingResult.data);
    }
    
    this.setState({
      lastUpdateTime: new Date().toLocaleString()
    });
    
  } catch (error) {
    this.handleError(error, '加载看板数据');
  } finally {
    this.setState({ loading: false });
  }
}

// 更新KPI卡片
export function updateKpiCards(kpiData) {
  // 今日销售额
  this.$('todaySalesCard').setValue(this.formatCurrency(kpiData.todaySales));
  
  // 本月销售额
  this.$('monthSalesCard').setValue(this.formatCurrency(kpiData.monthSales));
  
  // 目标完成率
  const completionRate = (kpiData.targetCompletion * 100).toFixed(1) + '%';
  this.$('targetCompletionCard').setValue(completionRate);
  
  // 设置完成率颜色
  const color = kpiData.targetCompletion >= 1 ? '#52c41a' : 
                kpiData.targetCompletion >= 0.8 ? '#faad14' : '#ff4d4f';
  this.$('targetCompletionCard').setStyle({ color });
  
  // 客户数量
  this.$('customerCountCard').setValue(kpiData.customerCount.toLocaleString());
}

// 更新销售趋势图
export function updateSalesTrendChart(trendData) {
  const chartConfig = {
    type: 'line',
    data: {
      labels: trendData.map(item => item.date),
      datasets: [{
        label: '销售额',
        data: trendData.map(item => item.sales),
        borderColor: '#1890ff',
        backgroundColor: 'rgba(24, 144, 255, 0.1)',
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: '销售趋势'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: (value) => this.formatCurrency(value)
          }
        }
      }
    }
  };
  
  this.$('salesTrendChart').setConfig(chartConfig);
}

// 设置自动刷新
export function setupAutoRefresh() {
  // 每5分钟自动刷新数据
  this.refreshTimer = setInterval(() => {
    this.loadDashboardData();
  }, 5 * 60 * 1000);
}

// 页面销毁时清理定时器
export function willUnmount() {
  if (this.refreshTimer) {
    clearInterval(this.refreshTimer);
  }
}

// 筛选条件变更
export function onFilterChange() {
  const dateRange = this.$('dateRangeField').getValue();
  const productCategory = this.$('productCategoryField').getValue();
  const salesRegion = this.$('salesRegionField').getValue();
  
  this.setState({
    filters: {
      dateRange,
      productCategory,
      salesRegion
    }
  });
  
  // 重新加载数据
  this.loadDashboardData();
}

// 导出数据
export async function exportDashboardData() {
  const { filters } = this.state;
  
  try {
    this.setState({ exporting: true });
    
    const result = await this.dataSourceMap.exportDashboardData.load({
      ...filters,
      format: 'excel'
    });
    
    if (result && result.success) {
      // 下载文件
      const link = document.createElement('a');
      link.href = result.data.downloadUrl;
      link.download = `销售数据看板_${this.utils.formatDate(new Date(), 'YYYY-MM-DD')}.xlsx`;
      link.click();
      
      this.utils.toast({
        type: 'success',
        title: '导出成功'
      });
    }
  } catch (error) {
    this.handleError(error, '导出数据');
  } finally {
    this.setState({ exporting: false });
  }
}

// 格式化货币
export function formatCurrency(amount) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount);
}
```

## 🛠️ 通用工具函数

```javascript
// 通用错误处理
export function handleError(error, context = '') {
  console.error(`${context}失败:`, error);
  
  let message = '系统异常，请稍后重试';
  
  if (error.message) {
    if (error.message.includes('网络')) {
      message = '网络连接异常，请检查网络后重试';
    } else if (error.message.includes('权限')) {
      message = '权限不足，请联系管理员';
    } else if (error.message.includes('参数')) {
      message = '参数错误，请检查输入信息';
    } else {
      message = error.message;
    }
  }
  
  this.utils.toast({
    type: 'error',
    title: `${context}失败`,
    message
  });
}

// 通用分页处理
export function handlePaginationChange(page, pageSize) {
  this.setState({
    pagination: {
      ...this.state.pagination,
      current: page,
      pageSize
    }
  });
  
  // 重新加载数据
  this.loadData();
}

// 通用表格行选择
export function handleTableRowSelection(selectedRowKeys, selectedRows) {
  this.setState({
    selectedRowKeys,
    selectedRows
  });
}

// 通用数据导出
export async function exportData(apiName, filename, params = {}) {
  try {
    this.setState({ exporting: true });
    
    const result = await this.dataSourceMap[apiName].load({
      ...params,
      format: 'excel'
    });
    
    if (result && result.success) {
      const link = document.createElement('a');
      link.href = result.data.downloadUrl;
      link.download = `${filename}_${this.utils.formatDate(new Date(), 'YYYY-MM-DD')}.xlsx`;
      link.click();
      
      this.utils.toast({
        type: 'success',
        title: '导出成功'
      });
    }
  } catch (error) {
    this.handleError(error, '导出数据');
  } finally {
    this.setState({ exporting: false });
  }
}
```

这些项目案例展示了宜搭平台在不同业务场景下的实际应用，为AI系统提供了丰富的实战经验和代码参考。
