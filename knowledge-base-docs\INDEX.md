﻿# Yida Developer Center - Knowledge Base Documents

This folder contains all Markdown documents from the Yida Developer Center project.

## Document Structure

- README.md - Main project documentation
- docs/ - Main documentation directory
  - api/ - API documentation
  - components/ - Component documentation
  - guide/ - User guides
  - tutorial/ - Tutorials
  - usage/ - Usage instructions
- src/pages/ - Page-related documentation

## Usage

These documents can be used as an AI knowledge base containing:
1. Complete introduction and user guides for Yida platform
2. API interface documentation and usage methods
3. Detailed component library descriptions
4. Development tutorials and best practices
5. Frequently asked questions and answers

