﻿# 宜搭开发者中心 - AI专家知识库

这是一个专为AI系统设计的宜搭开发专家知识库，包含了完整的宜搭平台开发文档和专家级指导内容。

## 🎯 知识库特色

### 专家级内容
- **EXPERT_GUIDE.md** - 宜搭开发专家指南，涵盖核心技术栈和架构设计
- **DEVELOPMENT_PATTERNS.md** - 开发模式与最佳实践详解
- **TROUBLESHOOTING_GUIDE.md** - 问题排查与解决方案大全
- **API_REFERENCE.md** - API完整参考指南
- **PROJECT_EXAMPLES.md** - 实战项目案例集

### 原始文档
- **README.md** - 项目主要说明文档
- **SUMMARY.md** - 详细文档汇总（113个文档）

## 📚 文档结构

### 🎓 专家指导文档
1. **EXPERT_GUIDE.md** - 成为宜搭开发专家的核心知识体系
   - 技术栈详解（React/Vue、JavaScript、Low-Code Engine）
   - 架构设计原则（低代码架构、双端适配、数据流）
   - 开发模式（可视化、代码、混合开发）
   - 组件体系（基础、表单、高级、自定义组件）
   - 数据管理（状态管理、数据源、数据绑定）
   - 生命周期管理、事件处理、API集成
   - 安全最佳实践、性能优化、调试技巧

2. **DEVELOPMENT_PATTERNS.md** - 开发模式与架构设计
   - 可视化开发模式（拖拽式开发、组件配置、布局设计）
   - 代码开发模式（JavaScript、SCSS样式开发）
   - 混合开发模式（可视化+代码结合）
   - 架构设计模式（MVC、组件化、状态管理）
   - 数据流管理（单向数据流、事件驱动）
   - 性能优化模式（懒加载、缓存策略）

3. **TROUBLESHOOTING_GUIDE.md** - 问题诊断与解决
   - 开发环境问题（设计器加载、组件拖拽）
   - 数据绑定问题（绑定失效、API调用失败）
   - 组件行为问题（表单验证、事件触发）
   - 性能问题（页面加载、内存泄漏）
   - 兼容性问题（浏览器、移动端适配）
   - 调试工具和技巧、错误处理最佳实践

4. **API_REFERENCE.md** - API完整参考
   - 宜搭JS-API（状态管理、组件操作、表单操作、数据源操作、工具函数）
   - 钉钉JS-API（设备信息、用户交互、扫码、地理位置）
   - 跨应用数据源API（表单数据操作、流程操作）
   - 服务端开放API（认证授权、数据操作）
   - API使用最佳实践（错误处理、缓存策略、批量操作）

5. **PROJECT_EXAMPLES.md** - 实战项目案例
   - 企业管理系统（员工管理、项目管理、CRM、库存管理）
   - 业务流程系统（请假审批、报销管理、采购申请、合同审批）
   - 数据展示系统（销售看板、运营监控、财务报表、设备监控）
   - 完整代码示例和实现细节

### 📖 原始文档库

#### API文档 (docs/api/)
- `about.md` - API概述和分类
- `yidaAPI.md` - 宜搭JS-API详细文档
- `dingAPI.md` - 钉钉JS-API使用指南
- `openAPI.md` - 跨应用数据源API
- `serverAPI.md` - 服务端开放API

#### 组件文档 (docs/components/)
- **高级组件** (15个): HTML、Iframe、JSX、气泡、横幅容器、过滤器、菜单、分页、进度条、搜索、滑块、步骤条、表格、时间轴、树形
- **基础组件** (9个): 按钮、对话框、抽屉、图标、图片、链接、链接块、文本、视频
- **表单组件** (16个): 附件、级联日期、级联选择、复选框、日期、编辑器、员工、图片、多选、数字、单选、评分、选择、表格、文本、文本域
- **布局组件** (3个): 容器、区域容器、标签页布局
- `interface.md` - 组件接口说明

#### 使用指南 (docs/guide/)
- **核心概念** (11个): 条件渲染、连接器、自定义组件、数据源、调试、事件处理、生命周期、循环渲染、状态管理、样式、数据校验
- **自定义组件** (6个): 属性配置、调试指南、常见问题、管理、快速开始、类型和设置器
- **FAQ** (4个): 常见问题解答
- 主要指南: 平台介绍、贡献指南、设计器使用、关键词说明、快速开始

#### 教程文档 (docs/tutorial/)
- `todoMVC.md` - TodoMVC完整开发教程

#### 使用说明 (docs/usage/)
- **创建应用指南** (4个): 从空白创建、从Excel创建、从模板创建、概述
- **平台功能** (16个): 应用中心、授权管理、基本信息、连接器工厂、企业效率、自定义开发、分发管理、ISV解决方案、我的应用、新建应用、订单中心、权限管理、角色管理、服务管理、开始使用、任务中心
- **平台介绍** (6个): 关于宜搭、平台优势、核心概念、设计器介绍、应用场景、工作台
- 其他文档: 使用说明、更新日志、联系我们、数据安全、关键词、新建应用、价格说明、快速开始、平台管理

## 🤖 AI使用指南

### 如何成为宜搭开发专家
1. **基础学习**: 从 `EXPERT_GUIDE.md` 开始，掌握核心技术栈和架构原理
2. **模式掌握**: 学习 `DEVELOPMENT_PATTERNS.md` 中的开发模式和最佳实践
3. **API精通**: 熟练掌握 `API_REFERENCE.md` 中的所有API使用方法
4. **实战练习**: 参考 `PROJECT_EXAMPLES.md` 中的项目案例进行实践
5. **问题解决**: 使用 `TROUBLESHOOTING_GUIDE.md` 快速诊断和解决问题

### 知识库特点
- **完整性**: 涵盖宜搭平台的所有核心功能和高级特性
- **专业性**: 包含专家级的架构设计和最佳实践指导
- **实用性**: 提供大量实际项目案例和代码示例
- **系统性**: 从基础概念到高级应用的完整知识体系
- **可操作**: 每个概念都配有具体的代码示例和实现方法

### 使用建议
- **新手开发者**: 从 `docs/guide/start.md` 和 `EXPERT_GUIDE.md` 开始
- **有经验开发者**: 重点学习 `DEVELOPMENT_PATTERNS.md` 和 `API_REFERENCE.md`
- **架构师**: 深入研究 `EXPERT_GUIDE.md` 中的架构设计部分
- **问题排查**: 优先查看 `TROUBLESHOOTING_GUIDE.md`
- **项目实战**: 参考 `PROJECT_EXAMPLES.md` 中的完整案例

## 📊 统计信息
- **总文档数**: 113个Markdown文档
- **专家指导文档**: 5个核心指南
- **API文档**: 5个完整API参考
- **组件文档**: 49个组件详细说明
- **概念文档**: 11个核心概念解析
- **实战案例**: 3个完整项目案例
- **最佳实践**: 涵盖开发、调试、优化的全方位指导

这个知识库将帮助AI系统成为真正的宜搭开发专家，能够解决各种复杂的开发问题并提供专业的技术指导。

