---
title: <PERSON>Field 明细
order: 16
---

# TableField 明细

## 何时使用

- 用于子表单提交场景；

## 类型定义
### 自定义操作项配置结构

```ts
interface Action {
  content: string; // 操作项文案
  callback: ({index: number, groupId: string, itemValue: any, actionKey: string }) => void; // 点击回调
  render: ({index: number, groupId: string, itemValue: any, actionKey: string }) => ReactNode; // 自定义渲染
}
```

## 组件示例

import Iframe from 'components/Iframe';

<Iframe url="https://www.aliwork.com/developer/table-field-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  category="form"
  dataSource={[
    {
      code: 'value',
      type: 'any[]',
      default: '[]',
      desc: '明细组件默认值',
    },
    {
      code: 'layoutSetting.layout',
      type: `'TILED' | 'TABLE'`,
      default: `'TABLE'`,
      desc: '排列方式（表格方式只在 PC 模式下有效），TILED：平铺方式、TABLE：表格方式',
    },
    {
      code: 'layoutSetting.theme',
      type: `'zebra' | 'split' | 'border'`,
      default: `'split'`,
      desc: '主题（只在 PC 表格方式下有效）',
    },
    {
      code: 'layoutSetting.showTableHead',
      type: 'boolean',
      default: 'true',
      desc: '是否显示表头（只在 PC 表格方式下有效）',
    },
    {
      code: 'layoutSetting.showIndex',
      type: 'boolean',
      default: 'true',
      desc: '是否显示序号',
    },
    {
      code: 'layoutSetting.indexName',
      type: 'string',
      default: `'项目'`,
      desc: '序号名称',
    },
    {
      code: 'layoutSetting.pcFreezeColumnStartCounts',
      type: `'0' | '1' | '2' | '3'`,
      default: '0',
      desc: '左侧列冻结，0表示不冻结',
    },
    {
      code: 'layoutSetting.isFreezeOperateColumn',
      type: 'boolean',
      default: 'true',
      desc: '是否冻结操作列',
    },
    {
      code: 'showActions',
      type: 'boolean',
      default: 'true',
      desc: '是否显示操作列',
    },
    {
      code: 'showCopyAction',
      type: 'boolean',
      default: 'false',
      desc: '是否显示复制按钮',
    },
    {
      code: 'copyButtonText',
      type: 'string',
      default: `'复制'`,
      desc: '复制按钮名称',
    },
    {
      code: 'showDelAction',
      type: 'boolean',
      default: 'true',
      desc: '是否显示删除按钮',
    },
    {
      code: 'delButtonText',
      type: 'string',
      default: `'删除'`,
      desc: '删除按钮名称',
    },
    {
      code: 'showDeleteConfirm',
      type: 'boolean',
      default: 'true',
      desc: '是否开启删除确认',
    },
    {
      code: 'showSortable',
      type: 'boolean',
      default: 'false',
      desc: '是否显示排序（只在 PC 表格方式下有效）',
    },
    {
      code: 'moveUp',
      type: 'string',
      default: `'上移'`,
      desc: '上移按钮名称',
    },
    {
      code: 'moveDown',
      type: 'string',
      default: `'下移'`,
      desc: '下移按钮名称',
    },
    {
      code: 'actions',
      type: 'Action[]',
      default: '[]',
      desc: '自定义操作项',
    },
    {
      code: 'addButtonPosition',
      type: `'bottom' | 'top'`,
      default: `'bottom'`,
      desc: '新增按钮位置',
    },
    {
      code: 'addButtonText',
      type: 'string',
      default: `'新增一项'`,
      desc: '新增按钮文案',
    },
    {
      code: 'addButtonBehavior',
      type: `'NORMAL' | 'DISABLED' | 'HIDDEN'`,
      default: 'NORMAL',
      desc: '新增按钮的可操作状态',
    },
    {
      code: 'pageSize',
      type: `'10' | '20' | '30'`,
      default: '20',
      desc: '分页条数',
    },
    {
      code: 'maxItems',
      type: 'number',
      default: '50',
      desc: '最大条数，最大 500 条',
    },
    {
      code: 'actionsColumnWidth',
      type: 'number',
      default: '70',
      desc: '操作列宽度，单位px',
    },
    {
      code: 'useCustomColumnsWidth',
      type: 'boolean',
      default: 'false',
      desc: '是否自定义其它列宽度',
    },
    {
      code: 'columnsWidth',
      type: 'Record<string, string>',
      default: '-',
      desc: `字段列宽，例如： { node_ockzdxe6of1: '200px' }`,
    },
    {
      code: 'onChange',
      type: '({ value: any , extra: any }) => void',
      default: '-',
      desc: 'onChange 子表单值变化',
    },
    {
      code: 'beforeAddClick',
      type: '() => boolean',
      default: '-',
      desc: '添加按钮点击前回调，返回false阻止添加',
    },
    {
      code: 'onAddClick',
      type: '( newGroupId: string ) => void',
      default: '-',
      desc: '添加按钮点击事件',
    },
    {
      code: 'beforeCopyClick',
      type: '( sourceGroupId: string, sourceItem: any ) => boolean',
      default: '-',
      desc: '复制按钮点击前回调，返回false阻止复制',
    },
    {
      code: 'onCopyClick',
      type: '( newGroupId: string , copiedItem: any ) => void',
      default: '-',
      desc: '添加按钮点击事件',
    },
    {
      code: 'beforeDelClick',
      type: '( groupId: string , item: object ) => void',
      default: '-',
      desc: '删除按钮点击前回调，返回false阻止删除',
    },
    {
      code: 'onDelClick',
      type: '( newGroupId: string , copiedItem: object ) => void',
      default: '-',
      desc: '删除按钮点击事件',
    },
    {
      code: 'beforeImportData',
      type: '( data: any[] ) => any[]',
      default: '-',
      desc: '批量导入数据前执行',
    },
  ]}
/>
