---
title: 组件调试
order: 5
---

## 简介

在宜搭应用内调试是指自定义组件在应用内**进行问题的排查及解决，使组件功能更加符合您的业务需求**。自定义组件的调试工作分为**简单调试**及**应用联调**两种模式。

## 简单调试

在低代码组件设计器中点击预览，可以进行对简单的属性配置进行测试，比如文本、布尔、数字。改变属性的值，可以看到一些效果。
![](https://img.alicdn.com/imgextra/i4/O1CN01hAwtn01SCE37qiCyP_!!6000000002210-2-tps-1706-908.png_.webp)
:::danger
**注意**：以下场景请使用 [应用联调](#hvYvc) 模式进行调试：

1. 所有和 function 相关的属性及配置都无法在简单的调试模式中使用；
2. 和组织、应用相关联的组件，比如上传图片、搜人等无法在简单的调试模式中使用；
:::

## 应用联调

自定义组件开发设计过程中任何与`function`相关的配置效果的调试都需要通过**应用联调**来进行调试，包括返回值的查看、函数中的`console.log()`代码的打印等。
**操作步骤：**

1. 创建一个**测试应用。**
1. 应用设置 >> 组件管理 >> 找到你开发的组件 >> 点击安装 >> 选择「0.1.0（开发版，组件修改可实时同步）」>> 点击安装。(如下图所示)

![](https://img.alicdn.com/imgextra/i1/O1CN01tTRvn01Zi0qYaNZjb_!!6000000003227-2-tps-1706-911.png_.webp)

3. 安装完成后，打开页面设计器，点击组件面板，点击 自定义组件，将刚才安装的组件拖入页面设计器。此时去组件设计器更改后，可以在页面设计器实时看到效果。（如下图所示）

![](https://img.alicdn.com/imgextra/i1/O1CN01Nhr1gV1GlhKqXA9mT_!!6000000000663-2-tps-1706-909.png_.webp)

:::danger

1. 不要在线上正式应用上调试组件，以免影响线上应用。
1. 如果自定义组件面板里看不到你的组件，请检查下安装范围是否正确。
:::
