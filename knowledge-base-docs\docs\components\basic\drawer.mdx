---
title: Drawer 抽屉
order: 8
---

# Drawer 抽屉

## 何时使用

抽屉是用于在不离开主路径的情况下，提供用户快速执行简单的操作、确认用户信息或反馈提示的辅助窗口。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/drawer-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'title',
      type: 'string',
      default: `'Drawer标题'`,
      desc: '抽屉标题',
    },
    {
      code: 'width',
      type: 'number',
      default: '-',
      desc: '抽屉框宽度',
    },
    {
      code: 'height',
      type: 'number',
      default: '-',
      desc: '抽屉框高度',
    },
    {
      code: 'visible',
      type: 'boolean',
      default: 'false',
      desc: '抽屉是否显示',
    },
    {
      code: 'hasMask',
      type: 'boolean',
      default: 'true',
      desc: '遮罩是否显示',
    },
    {
      code: 'placement',
      type: `'right' | 'top' | 'bottom' | 'left'`,
      default: `'right'`,
      desc: 'Drawer弹出位置',
    },
    {
      code: 'closeable',
      type: `'esc' | 'mask'`,
      default: `'esc'`,
      desc: '关闭对话框方式，esc：点击 ESC 按键关闭、mask：点击遮罩关闭',
    },
    {
      code: 'footer',
      type: 'boolean',
      default: 'true',
      desc: '底部按钮是否显示',
    },
    {
      code: 'footerAlign',
      type: `'left' | 'center' | 'right'`,
      default: `'right'`,
      desc: '对话框确定按钮和取消按钮对齐方式',
    },
    {
      code: 'footerActions',
      type: `'cancel,ok' | 'cancel,ok' | 'ok' | 'cancel'`,
      default: `'cancel,ok'`,
      desc: '对话框底部默认按钮排列方式，cancel代表取消按钮，ok代表确定按钮',
    },
    {
      code: 'onOk',
      type: '() => void',
      default: '-',
      desc: '点击确定按钮时事件',
    },
    {
      code: 'onCancel',
      type: '() => void',
      default: '-',
      desc: '点击取消按钮时事件',
    },
    {
      code: 'onClose',
      type: '() => void',
      default: '-',
      desc: 'Drawer关闭时事件',
    },
    {
      code: 'afterClose',
      type: '() => void',
      default: '-',
      desc: 'Drawer关闭后事件',
    },
    {
      code: 'afterOpen',
      type: '() => void',
      default: '-',
      desc: 'Drawer打开后事件',
    },
    {
      code: 'confirmStyle',
      type: `'primary' | 'warning' | 'ghostLight' | 'ghostDark'`,
      default: `'primary'`,
      desc: '确定按钮类型，参考 **Button** 组件的[文档](/docs/components/basic/button#组件属性)',
    },
    {
      code: 'cancelText',
      type: 'string',
      default: `'取消'`,
      desc: '取消按钮文案',
    },
    {
      code: 'confirmText',
      type: 'string',
      default: `'确定'`,
      desc: '确定按钮内文案',
    },
    {
      code: 'confirmState',
      type: `'NORMAL' | 'DISABLED' | 'LOADING'`,
      default: `'NORMAL'`,
      desc: '确定按钮的确认状态',
    },
  ]}
/>
