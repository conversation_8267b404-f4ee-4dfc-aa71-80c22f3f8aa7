---
title: Search 搜索
order: 12
---

# Search 搜索

## 何时使用

- 页面、表单数据搜索时使用。

## 组件示例

import Iframe from "components/Iframe";

<Iframe url="https://www.aliwork.com/developer/search-v2?isRenderNav=false" />

## 组件属性

import AttrTable from 'components/AttrTable';

<AttrTable
  dataSource={[
    {
      code: 'type',
      type: `'normal' | 'primary' | 'secondary' | 'dark'`,
      default: `'normal'`,
      desc: '搜索类型',
    },
    {
      code: 'defaultValue',
      type: 'string',
      default: '-',
      desc: '搜索框默认值',
    },
    {
      code: 'size',
      type: `'medium' | 'large'`,
      default: `'medium'`,
      desc: '尺寸',
    },
    {
      code: 'shape',
      type: `'normal' | 'simple'`,
      default: `'normal'`,
      desc: '搜索形状',
    },
    {
      code: 'autoFocus',
      type: 'boolean',
      default: 'false',
      desc: '是否自动聚焦',
    },
    {
      code: 'isFilter',
      type: 'boolean',
      default: 'false',
      desc: '是否开启选择器',
    },
    {
      code: 'defaultFilterValue',
      type: 'string',
      default: `'one'`,
      desc: '开启选择器按钮后，设置选择器默认值',
    },
    {
      code: 'filter',
      type: '[SearchDataSource[]](/docs/components/interface#searchdatasource)',
      default: `~~~json
[
  {
    "label": "one",
    "value": "one"
  },
  {
    "label": "two",
    "value": "two"
  },
  {
    "label": "three",
    "value": "three"
  }
]
      `,
      desc: '设置选择器的下拉列表值',
    },
    {
      code: 'dataSource',
      type: '[SearchDataSource[]](/docs/components/interface#searchdatasource)',
      default: `~~~json
[
  {
    "label": "天猫",
    "value": "value1"
  },
  {
    "label": "淘宝",
    "value": "value2"
  },
  {
    "label": "阿里巴巴",
    "value": "value3"
  }
]
      `,
      desc: '设置搜索框的下拉列表值',
    },
    {
      code: 'placeholder',
      type: 'string',
      default: `'请输入'`,
      desc: '搜索框默认提示',
    },
    {
      code: 'searchText',
      type: 'string',
      default: `'搜索'`,
      desc: '搜索按钮内容',
    },
    {
      code: 'hasClear',
      type: 'boolean',
      default: 'false',
      desc: '是否显示清除按钮',
    },
    {
      code: 'disabled',
      type: 'boolean',
      default: 'false',
      desc: '是否禁用',
    },
    {
      code: 'onChange',
      type: '(value: string) => void',
      default: '-',
      desc: '组件值发生改变事件',
    },
    {
      code: 'onSearch',
      type: '(value: string) => void',
      default: '-',
      desc: '点击搜索按钮触发的事件',
    },
    {
      code: 'onFilterChange',
      type: '(filterValue: string) => void',
      default: '-',
      desc: '选择器发生变化时的事件',
    },
  ]}
/>