---
title: 概述
order: 1
---

宜搭平台提供了非常丰富的开放 API 方案供开发者使用，针对不同的场景，开发者可以使用不同的开放 API 方案来实现业务诉求。不同的开放 API 的区别及适用场景如下所示：

- **宜搭 JS-API** - 主要用于在宜搭设计器的动作面板或者变量绑定场景调用一些前端功能 API，例如字符串格式化、获取表单控件值等，具体使用详见[宜搭 JS-API 文档](/docs/api/yidaAPI);
- **跨应用数据源 API** - 主要用于在宜搭设计器的远程 API 配置中使用，用于对宜搭的应用数据进行增删改查等操作，例如查询表单数据、流程发起等，具体使用详见[跨应用数据源 API 文档](/docs/api/openAPI);
- **钉钉 JS-API** - 主要用于搭建产物在钉钉端内调用一些钉钉客户端提供的 JSAPI，例如原生弹框、获取设备信息等，具体使用详见[钉钉 JS-API 文档](/docs/api/dingAPI);
- **服务端开放 API** - 主要用于在服务端调用宜搭提供的开放 API，功能和跨应用数据源 API 基本一致，但主要用于服务端调用，因此增加了鉴权环节，具体使用详见[服务端开放 API 文档](/docs/api/serverAPI);
