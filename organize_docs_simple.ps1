# Organize all Markdown files to a separate folder
# Exclude files in node_modules folder

# Create target directory
$targetDir = ".\knowledge-base-docs"
if (Test-Path $targetDir) {
    Remove-Item $targetDir -Recurse -Force
}
New-Item -ItemType Directory -Path $targetDir -Force

# Get all MD files, excluding node_modules
$mdFiles = Get-ChildItem -Path . -Recurse -Include "*.md","*.mdx" | Where-Object { $_.FullName -notlike "*node_modules*" }

Write-Host "Found $($mdFiles.Count) Markdown files"

# Copy files while maintaining directory structure
foreach ($file in $mdFiles) {
    # Get relative path
    $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart("\")
    
    # Create target path
    $targetPath = Join-Path $targetDir $relativePath
    $targetFolder = Split-Path $targetPath -Parent
    
    # Ensure target folder exists
    if (!(Test-Path $targetFolder)) {
        New-Item -ItemType Directory -Path $targetFolder -Force | Out-Null
    }
    
    # Copy file
    Copy-Item $file.FullName $targetPath -Force
    Write-Host "Copied: $relativePath"
}

Write-Host ""
Write-Host "Organization complete! All documents saved to '$targetDir' folder"
Write-Host "Directory structure maintained for easy AI understanding"

# Create an index file
$indexPath = Join-Path $targetDir "INDEX.md"
$indexContent = "# Yida Developer Center - Knowledge Base Documents`n`n"
$indexContent += "This folder contains all Markdown documents from the Yida Developer Center project.`n`n"
$indexContent += "## Document Structure`n`n"
$indexContent += "- README.md - Main project documentation`n"
$indexContent += "- docs/ - Main documentation directory`n"
$indexContent += "  - api/ - API documentation`n"
$indexContent += "  - components/ - Component documentation`n"
$indexContent += "  - guide/ - User guides`n"
$indexContent += "  - tutorial/ - Tutorials`n"
$indexContent += "  - usage/ - Usage instructions`n"
$indexContent += "- src/pages/ - Page-related documentation`n`n"
$indexContent += "## Usage`n`n"
$indexContent += "These documents can be used as an AI knowledge base containing:`n"
$indexContent += "1. Complete introduction and user guides for Yida platform`n"
$indexContent += "2. API interface documentation and usage methods`n"
$indexContent += "3. Detailed component library descriptions`n"
$indexContent += "4. Development tutorials and best practices`n"
$indexContent += "5. Frequently asked questions and answers`n"

Set-Content -Path $indexPath -Value $indexContent -Encoding UTF8
Write-Host "Created index file: INDEX.md"
